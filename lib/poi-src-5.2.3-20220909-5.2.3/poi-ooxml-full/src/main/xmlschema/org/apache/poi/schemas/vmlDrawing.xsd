<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<xsd:schema
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="urn:schemas-poi-apache-org:vmldrawing"
  targetNamespace="urn:schemas-poi-apache-org:vmldrawing"
>
  <xsd:import namespace="urn:schemas-microsoft-com:vml" schemaLocation="vml-main.xsd"/>
  <xsd:import namespace="urn:schemas-microsoft-com:office:office" schemaLocation="vml-officeDrawing.xsd"/>
  <xsd:import namespace="urn:schemas-microsoft-com:office:excel" schemaLocation="vml-spreadsheetDrawing.xsd"/>
  <xsd:element name="xml" type="CT_XML"/>
  <xsd:complexType name="CT_XML">
      <xsd:choice maxOccurs="unbounded">
        <xsd:any namespace="urn:schemas-microsoft-com:office:office"/>
        <xsd:any namespace="urn:schemas-microsoft-com:vml"/>
        <xsd:any namespace="urn:schemas-microsoft-com:office:excel"/>
      </xsd:choice>
  </xsd:complexType>
</xsd:schema>
