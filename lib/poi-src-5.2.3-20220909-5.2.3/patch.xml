<?xml version="1.0" encoding="UTF-8"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<!--
  =======================================================================
   Use Apache Ant to generate a patch file.
  =======================================================================
-->
<project name="create-patch" default="patchpackage" basedir=".">
    <property environment="env"/>
    <property name="patch.package" value="patch.tar.gz"/>
    <property name="patch.file" value="patch.txt"/>

    <condition property="svn.found">
        <or>
            <available file="svn" filepath="${env.PATH}"/>
            <available file="svn.exe" filepath="${env.PATH}"/>
            <available file="svn.exe" filepath="${env.Path}"/>
        </or>
    </condition>

    <target name="createpatch">
        <fail unless="svn.found" message="You need a version of svn to create the patch"/>
        <exec executable="svn" output="${patch.file}">
            <arg value="diff"/>
        </exec>
    </target>

    <target name="newfiles">
        <exec executable="svn" output="${patch.file}.tmp">
            <arg value="status"/>
        </exec>

       <!-- prepare the list of files to include in patch.tar.gz --> 
       <loadfile srcfile="${patch.file}.tmp" property="tar.file.list">  
         <filterchain>  
            <!-- capture any new files -->
            <linecontainsregexp>
                <regexp pattern="^(\?|A)......"/>
            </linecontainsregexp>
            <!-- filter out the first six characters -->
            <tokenfilter>
              <replaceregex pattern="^(.......)" replace=""/>
            </tokenfilter>
            <!--remove line breaks -->
            <striplinebreaks/>
          </filterchain> 
       </loadfile>  
    </target>

    <target name="patchpackage" depends="createpatch,newfiles">
       <delete file="${patch.package}"/> 
       <tar includes="${tar.file.list}"
            destfile="${patch.package}"   
            basedir="." 
            compression="gzip" >          
       </tar>  
       <delete file="${patch.file}.tmp"/>
    </target>
    
    <target name="apply"
        description="apply patch.tar.gz files generated by patchpackage">

            <!--
            extract patch.tar.gz to svn working copy
            $ tar xvzf patch.tar.gz
            -->
            <echo message="extracting ${patch.file} to working copy"/>
            <untar src="${patch.package}"
                dest="."
                failOnEmptyArchive="true"
                compression="gzip">
            </untar>

            <!--
            Apply the changes from patch.txt
            $ svn patch patch.txt
            -->
            <echo message="svn patch ${patch.file}"/>
            <fail unless="svn.found" message="You need a version of svn to create the patch"/>
            <exec executable="svn">
                <arg value="patch"/>
                <arg value="${patch.file}"/>
            </exec>
            <delete file="${patch.file}"/>
            
            <!--
            get a list of all new files in patch.tar.gz and store in tar.file.list filelist
            $ tar tf patch.tar.gz | grep -v patch\.txt | xargs svn add
            -->
            <echo message="Getting ${patch.file} file list"/>
            <delete dir="${patch.package}.tmp" quiet="false" failonerror="false" />
            <untar src="${patch.package}"
                dest="${patch.package}.tmp"
                failOnEmptyArchive="true"
                compression="gzip">
            </untar>
            <fileset dir="${patch.package}.tmp"
                id="tar.file.list"
                excludes="${patch.file}"/>
            
            <!-- add new files -->
            <echo message="Adding new files to svn working copy"/>
            <apply executable="svn"
                relative="true">
                <arg value="add"/>
                <fileset refid="tar.file.list"/>
            </apply>
            
            <delete dir="${patch.package}.tmp"/>

        </target>

</project>
