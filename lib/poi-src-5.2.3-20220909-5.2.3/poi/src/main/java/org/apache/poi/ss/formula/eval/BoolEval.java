/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

package org.apache.poi.ss.formula.eval;

public final class BoolEval implements NumericValueEval, StringValueEval {

    private final boolean _value;

    public static final BoolEval FALSE = new BoolEval(false);

    public static final BoolEval TRUE = new BoolEval(true);

    /**
     * Convenience method for the following:<br>
     * {@code (b ? BoolEval.TRUE : BoolEval.FALSE)}
     *
     * @return the {@code BoolEval} instance representing {@code b}.
     */
    public static BoolEval valueOf(boolean b) {
        return b ? TRUE : FALSE;
    }

    private BoolEval(boolean value) {
        _value = value;
    }

    public boolean getBooleanValue() {
        return _value;
    }

    @Override
    public double getNumberValue() {
        return _value ? 1 : 0;
    }

    @Override
    public String getStringValue() {
        return _value ? "TRUE" : "FALSE";
    }

    public String toString() {
        return getClass().getName() + " [" +
                getStringValue() +
                "]";
    }
}
