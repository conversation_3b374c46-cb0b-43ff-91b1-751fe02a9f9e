/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

package org.apache.poi.hssf.usermodel;

public interface HSSFShapeTypes {
    public static final int NotPrimitive = 0;
    public static final int Rectangle = 1;
    public static final int RoundRectangle = 2;
    public static final int Ellipse = 3;
    public static final int Diamond = 4;
    public static final int IsocelesTriangle = 5;
    public static final int RightTriangle = 6;
    public static final int Parallelogram = 7;
    public static final int Trapezoid = 8;
    public static final int Hexagon = 9;
    public static final int Octagon = 10;
    public static final int Plus = 11;
    public static final int Star = 12;
    public static final int Arrow = 13;
    public static final int ThickArrow = 14;
    public static final int HomePlate = 15;
    public static final int Cube = 16;
    public static final int Balloon = 17;
    public static final int Seal = 18;
    public static final int Arc = 19;
    public static final int Line = 20;
    public static final int Plaque = 21;
    public static final int Can = 22;
    public static final int Donut = 23;
    public static final int TextSimple = 24;
    public static final int TextOctagon = 25;
    public static final int TextHexagon = 26;
    public static final int TextCurve = 27;
    public static final int TextWave = 28;
    public static final int TextRing = 29;
    public static final int TextOnCurve = 30;
    public static final int TextOnRing = 31;
    public static final int StraightConnector1 = 32;
    public static final int BentConnector2 = 33;
    public static final int BentConnector3 = 34;
    public static final int BentConnector4 = 35;
    public static final int BentConnector5 = 36;
    public static final int CurvedConnector2 = 37;
    public static final int CurvedConnector3 = 38;
    public static final int CurvedConnector4 = 39;
    public static final int CurvedConnector5 = 40;
    public static final int Callout1 = 41;
    public static final int Callout2 = 42;
    public static final int Callout3 = 43;
    public static final int AccentCallout1 = 44;
    public static final int AccentCallout2 = 45;
    public static final int AccentCallout3 = 46;
    public static final int BorderCallout1 = 47;
    public static final int BorderCallout2 = 48;
    public static final int BorderCallout3 = 49;
    public static final int AccentBorderCallout1 = 50;
    public static final int AccentBorderCallout2 = 51;
    public static final int AccentBorderCallout3 = 52;
    public static final int Ribbon = 53;
    public static final int Ribbon2 = 54;
    public static final int Chevron = 55;
    public static final int Pentagon = 56;
    public static final int NoSmoking = 57;
    public static final int Star8 = 58;
    public static final int Star16 = 59;
    public static final int Star32 = 60;
    public static final int WedgeRectCallout = 61;
    public static final int WedgeRRectCallout = 62;
    public static final int WedgeEllipseCallout = 63;
    public static final int Wave = 64;
    public static final int FoldedCorner = 65;
    public static final int LeftArrow = 66;
    public static final int DownArrow = 67;
    public static final int UpArrow = 68;
    public static final int LeftRightArrow = 69;
    public static final int UpDownArrow = 70;
    public static final int IrregularSeal1 = 71;
    public static final int IrregularSeal2 = 72;
    public static final int LightningBolt = 73;
    public static final int Heart = 74;
    public static final int PictureFrame = 75;
    public static final int QuadArrow = 76;
    public static final int LeftArrowCallout = 77;
    public static final int RightArrowCallout = 78;
    public static final int UpArrowCallout = 79;
    public static final int DownArrowCallout = 80;
    public static final int LeftRightArrowCallout = 81;
    public static final int UpDownArrowCallout = 82;
    public static final int QuadArrowCallout = 83;
    public static final int Bevel = 84;
    public static final int LeftBracket = 85;
    public static final int RightBracket = 86;
    public static final int LeftBrace = 87;
    public static final int RightBrace = 88;
    public static final int LeftUpArrow = 89;
    public static final int BentUpArrow = 90;
    public static final int BentArrow = 91;
    public static final int Star24 = 92;
    public static final int StripedRightArrow = 93;
    public static final int NotchedRightArrow = 94;
    public static final int BlockArc = 95;
    public static final int SmileyFace = 96;
    public static final int VerticalScroll = 97;
    public static final int HorizontalScroll = 98;
    public static final int CircularArrow = 99;
    public static final int NotchedCircularArrow = 100;
    public static final int UturnArrow = 101;
    public static final int CurvedRightArrow = 102;
    public static final int CurvedLeftArrow = 103;
    public static final int CurvedUpArrow = 104;
    public static final int CurvedDownArrow = 105;
    public static final int CloudCallout = 106;
    public static final int EllipseRibbon = 107;
    public static final int EllipseRibbon2 = 108;
    public static final int FlowChartProcess = 109;
    public static final int FlowChartDecision = 110;
    public static final int FlowChartInputOutput = 111;
    public static final int FlowChartPredefinedProcess = 112;
    public static final int FlowChartInternalStorage = 113;
    public static final int FlowChartDocument = 114;
    public static final int FlowChartMultidocument = 115;
    public static final int FlowChartTerminator = 116;
    public static final int FlowChartPreparation = 117;
    public static final int FlowChartManualInput = 118;
    public static final int FlowChartManualOperation = 119;
    public static final int FlowChartConnector = 120;
    public static final int FlowChartPunchedCard = 121;
    public static final int FlowChartPunchedTape = 122;
    public static final int FlowChartSummingJunction = 123;
    public static final int FlowChartOr = 124;
    public static final int FlowChartCollate = 125;
    public static final int FlowChartSort = 126;
    public static final int FlowChartExtract = 127;
    public static final int FlowChartMerge = 128;
    public static final int FlowChartOfflineStorage = 129;
    public static final int FlowChartOnlineStorage = 130;
    public static final int FlowChartMagneticTape = 131;
    public static final int FlowChartMagneticDisk = 132;
    public static final int FlowChartMagneticDrum = 133;
    public static final int FlowChartDisplay = 134;
    public static final int FlowChartDelay = 135;
    public static final int TextPlainText = 136;
    public static final int TextStop = 137;
    public static final int TextTriangle = 138;
    public static final int TextTriangleInverted = 139;
    public static final int TextChevron = 140;
    public static final int TextChevronInverted = 141;
    public static final int TextRingInside = 142;
    public static final int TextRingOutside = 143;
    public static final int TextArchUpCurve = 144;
    public static final int TextArchDownCurve = 145;
    public static final int TextCircleCurve = 146;
    public static final int TextButtonCurve = 147;
    public static final int TextArchUpPour = 148;
    public static final int TextArchDownPour = 149;
    public static final int TextCirclePour = 150;
    public static final int TextButtonPour = 151;
    public static final int TextCurveUp = 152;
    public static final int TextCurveDown = 153;
    public static final int TextCascadeUp = 154;
    public static final int TextCascadeDown = 155;
    public static final int TextWave1 = 156;
    public static final int TextWave2 = 157;
    public static final int TextWave3 = 158;
    public static final int TextWave4 = 159;
    public static final int TextInflate = 160;
    public static final int TextDeflate = 161;
    public static final int TextInflateBottom = 162;
    public static final int TextDeflateBottom = 163;
    public static final int TextInflateTop = 164;
    public static final int TextDeflateTop = 165;
    public static final int TextDeflateInflate = 166;
    public static final int TextDeflateInflateDeflate = 167;
    public static final int TextFadeRight = 168;
    public static final int TextFadeLeft = 169;
    public static final int TextFadeUp = 170;
    public static final int TextFadeDown = 171;
    public static final int TextSlantUp = 172;
    public static final int TextSlantDown = 173;
    public static final int TextCanUp = 174;
    public static final int TextCanDown = 175;
    public static final int FlowChartAlternateProcess = 176;
    public static final int FlowChartOffpageConnector = 177;
    public static final int Callout90 = 178;
    public static final int AccentCallout90 = 179;
    public static final int BorderCallout90 = 180;
    public static final int AccentBorderCallout90 = 181;
    public static final int LeftRightUpArrow = 182;
    public static final int Sun = 183;
    public static final int Moon = 184;
    public static final int BracketPair = 185;
    public static final int BracePair = 186;
    public static final int Star4 = 187;
    public static final int DoubleWave = 188;
    public static final int ActionButtonBlank = 189;
    public static final int ActionButtonHome = 190;
    public static final int ActionButtonHelp = 191;
    public static final int ActionButtonInformation = 192;
    public static final int ActionButtonForwardNext = 193;
    public static final int ActionButtonBackPrevious = 194;
    public static final int ActionButtonEnd = 195;
    public static final int ActionButtonBeginning = 196;
    public static final int ActionButtonReturn = 197;
    public static final int ActionButtonDocument = 198;
    public static final int ActionButtonSound = 199;
    public static final int ActionButtonMovie = 200;
    public static final int HostControl = 201;
    public static final int TextBox = 202;
}
