/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

package org.apache.poi.ss.formula.ptg;

import java.util.Map;
import java.util.function.Supplier;

import org.apache.poi.ss.usermodel.FormulaError;
import org.apache.poi.util.GenericRecordUtil;
import org.apache.poi.util.LittleEndianInput;
import org.apache.poi.util.LittleEndianOutput;

/**
 * AreaErr - handles deleted cell area references.
 */
public final class AreaErrPtg extends OperandPtg {
    public static final byte sid = 0x2B;
    private final int unused1;
    private final int unused2;

    public AreaErrPtg() {
        unused1 = 0;
        unused2 = 0;
    }

    public AreaErrPtg(LittleEndianInput in)  {
        // 8 bytes unused:
        unused1 = in.readInt();
        unused2 = in.readInt();
    }

    public void write(LittleEndianOutput out) {
        out.writeByte(sid + getPtgClass());
        out.writeInt(unused1);
        out.writeInt(unused2);
    }

    public String toFormulaString() {
        return FormulaError.REF.getString();
    }

    @Override
    public byte getDefaultOperandClass() {
        return Ptg.CLASS_REF;
    }

    @Override
    public byte getSid() {
        return sid;
    }

    public int getSize() {
        return 9;
    }

    @Override
    public AreaErrPtg copy() {
        // immutable
        return this;
    }

    @Override
    public Map<String, Supplier<?>> getGenericProperties() {
        return GenericRecordUtil.getGenericProperties(
            "unused1", () -> unused1,
            "unused2", () -> unused2
        );
    }
}