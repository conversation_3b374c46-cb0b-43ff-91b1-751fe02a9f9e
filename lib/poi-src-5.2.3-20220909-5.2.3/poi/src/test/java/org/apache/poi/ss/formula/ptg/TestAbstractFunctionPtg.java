/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

package org.apache.poi.ss.formula.ptg;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.apache.poi.util.LittleEndianOutput;
import org.junit.jupiter.api.Test;

class TestAbstractFunctionPtg  {

    @Test
    void testConstructor() {
        FunctionPtg ptg = new FunctionPtg(1, 2, null, 255);
        assertEquals(1, ptg.getFunctionIndex());
        assertEquals(2, ptg.getDefaultOperandClass());
        assertEquals(255, ptg.getNumberOfOperands());
    }

    @Test
    void testInvalidFunctionIndex() {
        assertThrows(RuntimeException.class, () -> new FunctionPtg(40000, 2, null, 255));
    }

    @Test
    void testInvalidRuntimeClass() {
        assertThrows(RuntimeException.class, () -> new FunctionPtg(1, 300, null, 255));
    }

    private static class FunctionPtg extends AbstractFunctionPtg {

        protected FunctionPtg(int functionIndex, int pReturnClass,
                byte[] paramTypes, int nParams) {
            super(functionIndex, pReturnClass, paramTypes, nParams);
        }

        @Override
        public byte getSid() {
            return -1;
        }

        public int getSize() {
            return 0;
        }

        public void write(LittleEndianOutput out) {

        }

        @Override
        public FunctionPtg copy() {
            // immutable
            return this;
        }
    }
}
