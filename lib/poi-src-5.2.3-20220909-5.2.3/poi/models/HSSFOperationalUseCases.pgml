<?xml version="1.0" encoding="ISO-8859-1" ?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE pgml SYSTEM "pgml.dtd">
<pgml description="org.argouml.uml.diagram.use_case.ui.UMLUseCaseDiagram|-64--88-1-2-717d91:e7cd986e07:-7ffc"
      name="HSSFOperationalUseCases"
>
  <group name="Fig0"
       description="org.argouml.uml.diagram.use_case.ui.FigActor[128, 376, 58, 75]"
       href="-64--88-1-2-717d91:e7cd986e07:-7ff7"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig0.0"
      x="157"
      y="391"
      rx="10"
      ry="10"
      fill="1"
      fillcolor="-8355712"
      stroke="1"
      strokecolor="-8355712"
    />
    <ellipse name="Fig0.1"
      x="157"
      y="391"
      rx="10"
      ry="10"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <path name="Fig0.2"
      description="org.tigris.gef.presentation.FigLine"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="157"
              y="401" />
      <lineto x="157"
              y="421" />
    </path>
    <path name="Fig0.3"
      description="org.tigris.gef.presentation.FigLine"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="147"
              y="411" />
      <lineto x="167"
              y="411" />
    </path>
    <path name="Fig0.4"
      description="org.tigris.gef.presentation.FigLine"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="157"
              y="421" />
      <lineto x="152"
              y="436" />
    </path>
    <path name="Fig0.5"
      description="org.tigris.gef.presentation.FigLine"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="157"
              y="421" />
      <lineto x="162"
              y="436" />
    </path>
    <text name="Fig0.6"
      context=""
      x="128"
      y="437"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >ClientApps</text>
  </group>
  <group name="Fig1"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[144, 136, 100, 40]"
       href="-64--88-1-2-717d91:e7db573623:-7ff7"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig1.0"
      x="194"
      y="156"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig1.1"
      x="194"
      y="156"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig1.2"
      context=""
      x="157"
      y="149"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >create HSSF File</text>
  </group>
  <group name="Fig2"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[376, 144, 124, 40]"
       href="-64--88-1-2-717d91:e7db573623:-7fed"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig2.0"
      x="438"
      y="164"
      rx="62"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig2.1"
      x="438"
      y="164"
      rx="62"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig2.2"
      context=""
      x="401"
      y="157"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >write Workbook Entry</text>
  </group>
  <group name="Fig3"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[384, 280, 104, 40]"
       href="-64--88-1-2-717d91:e7db573623:-7fe8"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig3.0"
      x="436"
      y="300"
      rx="52"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig3.1"
      x="436"
      y="300"
      rx="52"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig3.2"
      context=""
      x="399"
      y="293"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >write sheet entry</text>
  </group>
  <group name="Fig4"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[392, 360, 100, 40]"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig4.0"
      x="442"
      y="380"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig4.1"
      x="442"
      y="380"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig4.2"
      context=""
      x="405"
      y="373"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >new MUseCase</text>
  </group>
  <group name="Fig5"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[392, 448, 104, 40]"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig5.0"
      x="444"
      y="468"
      rx="52"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig5.1"
      x="444"
      y="468"
      rx="52"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig5.2"
      context=""
      x="407"
      y="461"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >new MUseCase</text>
  </group>
  <group name="Fig6"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[392, 528, 100, 40]"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig6.0"
      x="442"
      y="548"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig6.1"
      x="442"
      y="548"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig6.2"
      context=""
      x="405"
      y="541"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >new MUseCase</text>
  </group>
  <group name="Fig7"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[384, 600, 100, 40]"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig7.0"
      x="434"
      y="620"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig7.1"
      x="434"
      y="620"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig7.2"
      context=""
      x="397"
      y="613"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >new MUseCase</text>
  </group>
  <group name="Fig8"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[376, 672, 100, 40]"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig8.0"
      x="426"
      y="692"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig8.1"
      x="426"
      y="692"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig8.2"
      context=""
      x="389"
      y="685"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >new MUseCase</text>
  </group>
  <group name="Fig9"
       description="org.argouml.uml.diagram.use_case.ui.FigUseCase[376, 752, 100, 40]"
       shown="0"
       fill="1"
       fillcolor="-1"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
    </private>
    <ellipse name="Fig9.0"
      x="426"
      y="772"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <ellipse name="Fig9.1"
      x="426"
      y="772"
      rx="50"
      ry="20"
      fill="1"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    />
    <text name="Fig9.2"
      context=""
      x="389"
      y="765"
      fill="0"
      fillcolor="-1"
      stroke="0"
      strokecolor="-16777216"
      font="dialog"
      textsize="9"
    >new MUseCase</text>
  </group>
  <group name="Fig10"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig1.0"
      sourceFigNode="Fig0"
      destFigNode="Fig1"
    </private>
    <path name="Fig10.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="381" />
      <lineto x="167"
              y="176" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig11"
       description="org.argouml.uml.diagram.ui.FigGeneralization"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig1.0"
      destPortFig="Fig2.0"
      sourceFigNode="Fig1"
      destFigNode="Fig2"
    </private>
    <path name="Fig11.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="244"
              y="160" />
      <lineto x="376"
              y="160" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig12"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig3.0"
      sourceFigNode="Fig0"
      destFigNode="Fig3"
    </private>
    <path name="Fig12.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="381" />
      <lineto x="384"
              y="320" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig13"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig4.0"
      sourceFigNode="Fig0"
      destFigNode="Fig4"
    </private>
    <path name="Fig13.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="400" />
      <lineto x="392"
              y="400" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig14"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig5.0"
      sourceFigNode="Fig0"
      destFigNode="Fig5"
    </private>
    <path name="Fig14.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="406" />
      <lineto x="392"
              y="448" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig15"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig6.0"
      sourceFigNode="Fig0"
      destFigNode="Fig6"
    </private>
    <path name="Fig15.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="406" />
      <lineto x="392"
              y="528" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig16"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig7.0"
      sourceFigNode="Fig0"
      destFigNode="Fig7"
    </private>
    <path name="Fig16.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="406" />
      <lineto x="384"
              y="600" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig17"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig9.0"
      sourceFigNode="Fig0"
      destFigNode="Fig9"
    </private>
    <path name="Fig17.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="406" />
      <lineto x="376"
              y="752" />
    </path>
    <annotations>
    </annotations>
  </group>
  <group name="Fig18"
       description="org.argouml.uml.diagram.ui.FigAssociation"
       stroke="1"
       strokecolor="-16777216"
  >
    <private>
      sourcePortFig="Fig0.0"
      destPortFig="Fig8.0"
      sourceFigNode="Fig0"
      destFigNode="Fig8"
    </private>
    <path name="Fig18.1"
      description="org.tigris.gef.presentation.FigPoly"
      fill="0"
      fillcolor="-1"
      stroke="1"
      strokecolor="-16777216"
    >
      <moveto x="167"
              y="406" />
      <lineto x="376"
              y="672" />
    </path>
    <annotations>
    </annotations>
  </group>
</pgml>
