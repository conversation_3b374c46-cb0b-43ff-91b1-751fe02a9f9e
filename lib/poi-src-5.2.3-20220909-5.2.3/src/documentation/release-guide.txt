# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.


             ==============================
                   POI Release Guide
             ==============================


(I) Prerequisites

   1. You should read the <a href="https://www.apache.org/dev/release.html">Apache Release FAQ</a>
   2a. You must have shell access to people.apache.org; and you should
       have key-based authentication set up
       1. Generate ssh key with ssh-keygen -t rsa -b 4096
       (e.g. <a href="http://www.linuxproblem.org/art_9.html">how to</a>.)
       2. Add contents of id_rsa.pub to SSH Key (authorized_keys) line on https://id.apache.org/
       3. ssh -v <EMAIL>
          Verify authenticity of host: https://www.apache.org/dev/machines
       4. Only sftp access is necessary
   2b. You must be a member of the committee group
   3. Release manager must have their public key appended to the KEYS file checked in to SVN and the key published on one of the public key servers.
      More info can be found here: <a href="https://www.apache.org/dev/release-signing.html">https://www.apache.org/dev/release-signing.html</a>
   4. You must have Java JDK 1.8 installed and active.
   5. You must have the following utilities installed on your local machine and available in your path:
          * <a href="www.openssh.com">ssh</a>
          * <a href="www.gnupg.org">gnupg</a>
          * <a href="www.openssl.org">openssl</a>
      For Windows users, install Cygwin and make sure you have the above utilities
   6a. The POI build system requires two components to perform a build
          * <a href="https://ant.apache.org">Ant</a> 1.9.x or higher
          * <a href="https://forrest.apache.org/">Forrest</a> 0.90.
      Make sure ANT_HOME and FORREST_HOME are set.

   6b. To deploy with Maven, you should have the latest stable Maven 2.x.
        POI 3.10-beta2 was deployed with Maven 2.2.1.

   6c. Ensure you can log in to https://repository.apache.org/ with your Apache
        credentials, and that you can see the "Staging Repositories" area on
        the left hand side.

   6d. Ensure your ~/.m2/settings.xml contains a server entry with your
        Apache credentials in, to be used for uploading. Something like:

{code:xml}
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0">
  <servers>
        <server>
            <!-- used for uploading to nexus staging repository and svn commit -->
            <id>apache-id</id>
            <username>apache-userid</username>
            <password>apache-passwd</password>
        </server>
        <server>
            <!-- used for signing the releases -->
            <id>poi-signing</id>
            <username>keyid (key fingerprint, no spaces)</username>
            <passphrase>password</passphrase>
            <privateKey>../.gnupg/secring.gpg</privateKey>
        </server>
    </servers>
</settings>
{code}

   6e. It's a good idea to check at https://ci-builds.apache.org/job/POI/
       that Jenkins is in a good state (i.e. most recent build passed
       and is up to date with SVN). You probably also want to e-mail
       the dev list with a note to say you're building a release.

   7. Before building, you should run the "rat-check" build task, which
      uses <a href="https://incubator.apache.org/rat/">Apache Rat</a>
      to check the source tree for files lacking license headers. Files
      without headers should be either fixed, or added to the exclude list

   8. Check file permissions are correct in SVN.
      There can be  files in the SVN tree marked executable (have the
      svn:executable property set), but which should not be. Checking them
      out will cause the executable bit to be set for them on filesystems
      which support it. The flag can be removed in batch using

{code:sh}
svn pd 'svn:executable' $(find -name .svn -prune -or -type f ! -name \*.sh \
 -print0 | xargs -0 svn pg 'svn:executable' | cut -d ' ' -f 1)
{code}

   9. Before building, ensure that the year in the NOTICE file is correct,
      and review any new or updated dependencies to ensure that if they
      required LICENSE or NOTICE updates then these were done.

  10. Ensure that the changelog is up to date

  11. Ensure that the KEYS files in the dist areas are up-to-date with the
      latest ones in svn:
      https://dist.apache.org/repos/dist/dev/poi/KEYS
      https://dist.apache.org/repos/dist/release/poi/KEYS
      Dist is a regular svn repo that can be checked out and committed to.
      To upload to dist: https://www.apache.org/dev/release-distribution


(II) Making release artefacts
  This is mostly automated with a sequence of 3 release-prep ant targets.
  Run these commands from a clean checkout of https://svn.apache.org/repos/asf/poi/trunk

  1. The following command will update the documentation and create/download the tag (needs a Java7+ environment)
     using your Apache credentials stored in ~/.m2/settings.xml.
     {code:sh}
     ant -Dversion.id=FIXME3.15 release-prep1
     {code}

  2. Compile the dist (needs a JDK8 environment, sudo update-alternatives --config java)
     {code:sh}
     ant release-prep2
     {code}

  3. Upload to the nexus repo and to the svn release candidate repo  (needs a Java7+ environment)
     The nexus repo is located at https://repository.apache.org/
     The release candidate repo is located at https://dist.apache.org/repos/dist/dev/poi/(4.0.0-RC1)
     {code:sh}
     ant -Dversion.id=FIXME3.15 -Drelease.rc=RC1 release-prep3
     {code}

  4. Summarise key changes, and any backwards incompatible ones,
     in the partially auto-generated ./build/release/build/dist/RELEASE-NOTES.txt
     which needs to be committed separately

  5. validate signatures

Check your signed files

{code:sh}
find . -name "*.md5" -type f -execdir md5sum -c {} \;
find . -name "*.sha1" -type f -execdir sha1sum -c {} \;
find . -name "*.sha256" -type f -execdir sha256sum -c {} \;
find . -name "*.sha512" -type f -execdir sha512sum -c {} \;
find . -name "*.asc" -exec gpg --no-secmem-warning --verify {} \;
{code}

 (III) Calling the vote:

   1. The release manager should call the vote
   2. Include the URL of the release artifacts
   3. Include the time for the vote to run (3 day minimum, can be longer)
   4. Provide guidance on what needs to be checked
   5. Complete a tally, and send a result once the time has passed

 (IV) After the vote:

Deploy the artifacts from the staging area (https://dist.apache.org/repos/dist/dev/poi/)
to the release area of the dist repo:
    https://dist.apache.org/repos/dist/release/poi/release/

Perform a sparse checkout of the dist repo to move artifacts in the staging area to the release area
In the following example, replace FIXME3.16-RC1 or FIXME3.16 with the version you are releasing

{code:sh}
svn checkout https://dist.apache.org/repos/dist/ --depth immediates
svn update --set-depth immediates dist/dev/poi/
svn update --set-depth infinity   dist/dev/poi/FIXME3.16-RC1/
svn update --set-depth infinity   dist/release/poi/
svn rm dist/release/poi/release/src/* dist/release/poi/release/bin/* dist/release/poi/release/maven/
svn mv dist/dev/poi/FIXME3.16-RC1/poi-src-* dist/release/poi/release/src/
svn mv dist/dev/poi/FIXME3.16-RC1/poi-bin-* dist/release/poi/release/bin/
svn mv dist/dev/poi/FIXME3.16-RC1/maven/ dist/release/poi/release/
svn mv dist/dev/poi/FIXME3.16-RC1/RELEASE-NOTES.txt dist/release/poi/dev/RELEASE-NOTES-FIXME3.16.txt
# regular (non-svn) copy so that these text files retain their revision history
cp dist/release/poi/dev/RELEASE-NOTES-FIXME3.16.txt dist/release/poi/release/RELEASE-NOTES.txt
cp dist/dev/poi/KEYS dist/release/poi/KEYS
svn ci dist/ -m "deploy 3.16 release artifacts from staging area"
{code}


And remove any old releases from the staging area if they exist (should have been deleted by Step 11)
Staging area: https://dist.apache.org/repos/dist/dev/poi/

{code:sh}
svn rm https://dist.apache.org/repos/dist/dev/poi/FIXME3.16-RC1 -m "remove old release from staging area"
{code:sh}

You should get an email from the Apache Reporter Service (<EMAIL>)
at your Apache email address.
The email instructions will ask you to log on to https://reporter.apache.org/addrelease.html?poi
and add your release data (version and date) to the database.

Log into https://repository.apache.org/ and go to the "Staging Repositories" area.
Find the "orgapachepoi" entry, check it has the right content, then Close the repository
(it was probably already closed by release-prep3).
Select all artifacts and Release (and Automatically Drop) them.
Refresh to verify that the artifacts are no longer in the Staging Repositories area.

2. Wait for the distributions to appear on your favourite mirror (anywhere from 3-24 hours)
  https://www.apache.org/dyn/closer.lua/poi/dev/

3. Wait for the maven artifacts to appear on Maven Central, and ensure they work:
  Maven Central: https://search.maven.org/#search|ga|1|g%3A%22org.apache.poi%22

Create a simple project and make sure the release artifacts are accessible
by maven:

{code:sh}
mvn archetype:create -DgroupId=org.apache.poi.scratchpad -DartifactId=maven-test
cd maven-test
{code}

edit pom.xml and add the release artefacts to the project dependencies:

{code:xml}
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>4.0.0</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-scratchpad</artifactId>
      <version>4.0.0</version>
    </dependency>
{code}

edit src/main/java/Test.java and add this:

{code:java}
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

public class Test {}
{code}

{code:sh}
mvn compile
{code}

You should see [INFO] BUILD SUCCESSFUL in the end, which tells you that
the jars could be downloaded fine.

4. Edit the website homepage and list the new release there.
   * poi/site/src/documentation/content/xdocs/index.xml
   * poi/site/src/documentation/content/xdocs/changes.xml
   remove older releases.

5. Edit the website download page, and list the new release there. This should
   reference the checksums, so take care when updating
   * poi/site/src/documentation/content/xdocs/download.xml
{code:sh}
# the following generates a download-snipplet.xml to be copy&pasted in the download.xml
ant update-download -Dversion.id="3.15" -Dreltype=dev -Drel_date="02 July 2016" -Dfile_date="20160702"
{code}
   And copy the contents from the output, download-snipplet.xml, to the appropriate section
   in poi/site/src/documentation/content/xdocs/download.xml.

   Additionally there are some further files to be updated ... check the results and commit them:
{code:sh}
# the following updates various references from the previous release to the current release
ant release-finish -Dfile_date="20160702"
{code}


6. Build site using a recent version of Java 1.8
   Commit the site changes to svn, and publish live

7. Copy the build javadocs to a stable location under /apidocs/{ver}/
   - For a major release, create a new subfolder to hold the javadocs for
     this release family, eg 4.1 for 4.1.x
   - For a minor release, replace the existing subfolder for the release
     family, eg 4.1.2 uses 4.1 replacing the previous 4.1.1

8. Don't forget to upload the latest version of the site and javadocs

9. Send announcements:
From: your @apache.org e-mail address
To: <EMAIL>, <EMAIL>, <EMAIL>, and <EMAIL>
Subject: [ANNOUNCE] Apache POI FIXME3.16 released
Body:
"""
The Apache POI PMC is pleased to announce the release of Apache POI FIXME3.16.

Apache POI is a Java library for reading and writing Microsoft Office files.

For detailed changes in this release, refer to the release notes [1] and the changelog [2].

Thank you to all our contributors for making this release possible.

On behalf of the Apache POI PMC,
Your Name

[1] Release notes: https://www.apache.org/dyn/closer.lua/poi/dev/RELEASE-NOTES-FIXME3.16.txt
[2] Changelog: https://poi.apache.org/changes.html#FIXME3.16
"""

Note, announcements should be sent from your @apache.org e-mail address.

10. In Bugzilla, add a new version and the next "...-dev" version. Also close the n-2 -dev version to new bugs.

11. Add the version to the DOAP file too
  https://svn.apache.org/repos/asf/poi/trunk/doap_POI.rdf

12. Delete directory that held RC.

e.g.
{code:sh}
svn delete -m "delete empty RC directory for 4.0.0" https://dist.apache.org/repos/dist/dev/poi/4.0.0-RC1
{code}
