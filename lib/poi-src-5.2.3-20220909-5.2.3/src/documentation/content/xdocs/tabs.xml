<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE tabs PUBLIC "-//APACHE//DTD Cocoon Documentation Tab V1.1//EN" "http://forrest.apache.org/dtd/tab-cocoon-v11.dtd">

<tabs software="POI" title="POI" copyright="The Apache Software Foundation">

  <!-- The rules are:
    @dir will always have /index.html added.
    @href is not modified unless it is root-relative and obviously specifies a
    directory (ends in '/'), in which case /index.html will be added
  -->

  <tab id="home" label="Home" dir=""/>
  <tab id="help" label="Help" dir="help/"/>
  <tab id="components" label="Component APIs" dir="components/"/>
  <tab id="community" label="Getting Involved" dir="devel/"/>

</tabs>
