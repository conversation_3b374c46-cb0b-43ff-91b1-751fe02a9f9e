<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd">

<document>
  <header>
    <title>Apache POI - In the News over the world</title>
    <authors>
      <person id="AO" name="Andrew C. Oliver" email="<EMAIL>"/>
      <person id="TK" name="Tetsuya Kitahata" email="<EMAIL>"/>
    </authors>
  </header>

  <body>
    <section><title>POI in the news</title>
        <p>
                These are articles/etc. posted about POI around the web.  If you
                see POI in the news or mentioned at least somewhat prominently
                on a site (not your homepage that you put the work POI on in
                order to get us to link you and by the why here is a picture of
                your wife in kids) then send a patch to the list.  In general
                equal time will be given so please feel free to send inflammatory
                defamation as well as favorable, technical and factual.  Really
                stupid things won't be mentioned (sorry).
        </p>
    </section>
    <section><title>English</title>
        <ul>
                <li>
                      <a href="http://archive.midrange.com/web400/200204/msg00023.html">Discussion about using POI on AS/400s</a>
                </li>
                <li>
                      <a href="http://www.somelist.com/mails/23819.html">Discussion from back when we almost had POI as the filter for KOffice if politics and licenses hadn't killed iit</a>
                </li>
                <li>
                       <a href="http://www.oreillynet.com/pub/wlg/1552?page=last&amp;x-showcontent=text">Java discussion on O'Reilly Network including discussion about POI</a> - O'Reilly.net
                </li>
                <li>
                       <a href="http://www.rollerweblogger.org/page/roller/20020715">Poor Obfuscation Implementation.</a> - Blog of David M. Johnson
                </li>
                <li>
                       <a href="http://www.jsurfer.org/article.php?sid=322">
                        POI 1.5-dev-rc2 released </a> - JSurfer
                </li>

                <li>
                       <a href="http://directory.google.com/Top/Computers/Programming/Languages/Java/Class_Libraries/Data_Formats/Microsoft_Formats/"> Google says we're the most important in our category </a>
                </li>
                <li>
                       <a href="http://www.javaworld.com/javaworld/javaqa/2002-05/01-qa-0503-excel3.html">It's POI-fect</a> - Tony Sintes, Javaworld
                </li>
                <li>
                       <a href="http://www.need-a-cake.com/categories/cocoonWeblog/2002/03/07.html">
			Nicola announces POI serialization code
                       </a> - Matthew Langham's Radio Weblog
                </li>
                <li>
                        <a href="http://javalobby.org/discussionContext/showThreaded/frm/javalobby?folderId=20&amp;discussionContextId=11523">
                        Jakarta POI 1.4583 Released</a> - JavaLobby
                </li>
                <li>
                        <a href="http://javalobby.org/discussionContext/showThreaded/frm/javalobby?discussionContextId=11442&amp;folderId=20">
                        POI project moves to Jakarta (OLE 2 CDF/Excel/Word in
                        pure java)</a> - JavaLobby
                </li>
                <li>
                        <a
                        href="http://www.geocities.com/marcoschmidt.geo/java-image-coding.html">
                        List of Java libraries to read and write image and document files
                        </a> Marco Schmidt's homepage (normally we wouldn't
                        feature someone's homepage but its an extensive list of
                        information including "alternatives to POI" (for those
                        of you who are very wealthy).  But heck I think I'll
                        bookmark his page for myself since he's like got every
                        piece of info known to man linked or featured on it!
                </li>
                <li>
                        <a href="http://radio.weblogs.com/0101350/">
                        The Experiences of an Operator (M&#229;ns af Klercker)
                        </a> - radio.weblogs.com
                </li>
                <li>
                        <a href="http://dataconv.org/apps_office.html">
                        DATACONV - Data Conversion Tools: Office
                        </a> DATACONV
                </li>
                <li>
                        <a href="http://chicago.sourceforge.net/devel/">
                        Chicago Developer Page
                        </a>
                </li>
                <li>
                        <a href="http://www.onjava.com/pub/d/1157">
                        POI/POI Serialization Project
                        </a> - Man you know you've hit the bigtime when
                        O'Reilly Likes you..  ;-)
                </li>
                <li>
                        <a
                        href="http://www.javaworld.com/netnews/index.shtml">
                        News Around the Net
                        </a> - Java World
                </li>

        </ul>
    </section>
    <section><title>Nederlandstalige (Dutch)</title>
        <ul>
                <li>
                        <a
                        href="http://www.ster.be/java/java9.html">
                        Een Excel-werkboek maken vanuit Java - Lieven Smits
                        </a>
                </li>
        </ul>
    </section>
    <section><title>Deutsch (German)</title>
        <ul>
                <li> <a
                      href="http://www.entwickler.com/itr/news/show.php3?id=6132&amp;nodeid=82 ">Apache POI verffentlicht</a> - entwicker.com
                </li>
                <li>
                       <a
                       href="http://www.jsp-develop.de/newsletter/10/">
                       Apache Jakarta-Projekt bringt Word und Excel in die Java-Welt </a> - jsp-develop.de  (for the misguided who use JSP ;-) )
                </li>
                <li>
                        <a
                        href="http://www.entwickler.com/news/2002/02/5718/news.shtml">
                        Neues Apache-Projekt bringt Word- und Excel nach Java
                        </a> - entwickler.com
                </li>
        </ul>
    </section>
    <section><title>Espa&#241;ol (Spanish)</title>
        <ul>
                <li>
                        <a href="http://www.javahispano.com/noticias/todas.jsp">
                        OLE2 desde Java nativo
                        </a> - javaHispano
                </li>
                <li>
                      <a href="http://p2p.wrox.com/archive/java_espanol/2002-08/3.asp">Spanish discussion about Excel and Java including POI from Wrox forums</a>
                </li>
        </ul>
    </section>
    <section><title>Fran&#231;ais (French)</title>
        <ul>
		<li>
			<a href="http://linuxfr.org/section/D%E9veloppeur,0,1,8,0.html">
			Excel/OLE accessibles
                        </a> - Da Linux French Page
                </li>
                <li>
                        <a href="http://www.sogid.com/javalist/f2002/traiter_word_java.html">Discussion on POI in French</a>
                </li>
        </ul>
    </section>
    <section><title>Nihongo (Japanese)</title>
        <ul>
                <li>
                        <a href="http://drpanda.freezope.org/Memo/docs/jakarta/poi/poi_sample">100% PureJava...</a> - Dr. Panda Portal
                </li>
                <li>
                        <a
                        href="http://www.gimlay.org/~andoh/java/javanew.html">
                        What's new with java?
                        </a> - gimlay.org
                </li>
                <li><a href="http://taka-2.com/jclass/POI/">Java de Excel</a> - How to use Japanese with POI</li>
                <li><a href="http://www.tech-arts.co.jp/macosx/webobjects-jp/htdocs/3200/3218.html">Various discussion in Japanese including on POI</a></li>
                <li><a href="http://muimi.com/j/jakarta/">Japanese resources on Jakarta projects including POI</a></li>
                <li><a href="http://www.fk.urban.ne.jp/home/<USER>/">Kishida's site</a> -- Weekly Forte Lectures -- includes a snip about POI and Japanese.</li>

        </ul>
    </section>
    <section><title>Russkii Yazyk (Russian)</title>
        <ul>
          <li>
             <a href="http://www.nestor.minsk.by/kg/kg02/21/kg22108.html">
             Probably a translation of the Javalobby announcement of 1.5-final
             </a> -- Computer News (What's New)
          </li>
        </ul>
    </section>
    <section><title>Hangul (Korean)</title>
        <ul>
           <li>
             <a href="http://www.javabrain.co.kr/AnswerView?questionId=1189&amp;categoryId=8">Various discussion in Korean about Excel output/APIs including POI</a>
           </li>
        </ul>
    </section>
    <section><title>No freaking idea</title>
        <p>
                If you can read one of these languages, send mail to the list
                telling us what language it is and we'll categorize it!
        </p>
        <ul>
                <li>
                       <a
                       href="http://www.javacentrix.com/index.htm">
                       If I had to guess, I'd say this is Thai, but
                       maybe you actually know</a> - javacentrix.com
                </li>
        </ul>
    </section>
  </body>
  <footer>
    <legal>
      Copyright (c) @year@ The Apache Software Foundation All rights reserved.
      <br />
      Apache POI, POI, Apache, the Apache feather logo, and the Apache 
      POI project logo are trademarks of The Apache Software Foundation.
    </legal>
  </footer>
</document>
