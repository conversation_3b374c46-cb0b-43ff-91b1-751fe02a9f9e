<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd">

<document>
    <header>
        <title>POI-HSLF and and POI-XLSF - Java API To Access Microsoft Powerpoint Format Files</title>
        <subtitle>Overview</subtitle>
        <authors>
            <person name="Avik Sengupta" email="avik at apache dot org"/>
            <person name="Nick Burch" email="nick at apache dot org"/>
            <person name="Yegor Kozlov" email="yegor at apache dot org"/>
        </authors>
    </header>

    <body>
        <section>
            <title>POI-HSLF</title>

            <p>HSLF is the POI Project's pure Java implementation of the Powerpoint '97(-2007) file format. </p>
    <p>HSLF provides a way to read, create or modify PowerPoint  presentations. In particular, it provides:
            </p>
            <ul>
                <li>api for data extraction (text, pictures, embedded objects, sounds)</li>
                <li>usermodel api for creating, reading and modifying ppt files</li>
            </ul>
         <note>
            This code currently lives the
            <a href="https://svn.apache.org/viewvc/poi/trunk/poi-scratchpad/">scratchpad area</a>
            of the POI SVN repository. To use this component, ensure
            you have the Scratchpad Jar on your classpath, or a dependency
            defined on the <em>poi-scratchpad</em> artifact - the main POI
            jar is not enough! See the
            <a href="site:components">POI Components Map</a>
            for more details.
			</note>
			<p>The <a href="./quick-guide.html">quick guide</a> documentation provides
            information on using this API. Comments and fixes gratefully accepted on the POI
            dev mailing lists.</p>
        </section>
        <section>
            <title>POI-XSLF</title>
            <p>
            XSLF is the POI Project's pure Java implementation of the PowerPoint 2007 OOXML (.xlsx) file format.
            Whilst HSLF and XSLF provide similar features, there is not a common interface across the two of them at this time.
            </p>
            <p>
            Please note that XSLF is still in early development and is a subject to incompatible changes in future.
            </p>
            <p>
              A quick guide is available in the <a href="./xslf-cookbook.html">XSLF Cookbook</a>
            </p>
        </section>
    </body>
</document>
