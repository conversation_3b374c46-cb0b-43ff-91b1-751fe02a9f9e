<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd">
<document>
  <header>
    <title>Apache POI - POIFS - Java implementation of the OLE 2 Compound Document format</title>
    <subtitle>Overview</subtitle>
    <authors>
      <person name="Andrew C. Oliver" email="<EMAIL>"/>     
      <person name="Nicola Ken Barozzi" email="<EMAIL>"/>     
    </authors>
  </header>
  <body>
    <section><title>Overview</title>
      <p>POIFS is a pure Java implementation of the OLE 2 Compound
         Document format.</p>
      <p>By definition, all APIs developed by the POI project are
         based somehow on the POIFS API.</p>
      <p>A common confusion is on just what POIFS buys you or what OLE
         2 Compound Document format is exactly. POIFS does not buy you
         DOC, or XLS, but is necessary to generate or read DOC or XLS
         files. You see, all file formats based on the OLE 2 Compound
         Document Format have a common structure. The OLE 2 Compound
         Document Format is essentially a convoluted archive
         format. Think of POIFS as a "zip" library. Once you can get
         the data in a zip file you still need to interpret the
         data. As a general rule, while all of our formats <em>use</em>
         POIFS, most of them attempt to abstract you from it. There
         are some circumstances where this is not possible, but as a
         general rule this is true.</p>
      <p>If you're an end user type just looking to generate XLS
         files, then you'd be looking for HSSF not POIFS; however, if
         you have legacy code that uses MFC property sets, POIFS is
         for you!  Regardless, you may or may not need to know how to
         use POIFS but ultimately if you use technologies that come
         from the POI project, you're using POIFS underneath.  Perhaps
         we should have a branding campaign "POIFS Inside!".  ;-)</p>

    </section>
  </body>
</document>
