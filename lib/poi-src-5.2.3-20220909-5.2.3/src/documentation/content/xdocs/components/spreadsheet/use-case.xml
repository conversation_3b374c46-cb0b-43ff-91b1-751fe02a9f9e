<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd"> 
     
<document>
  <header>
   <title>HSSF Use Cases</title>
   <authors>
    <person email="<EMAIL>" name="Marc Johnson" id="MJ"/>
   </authors>
  </header>
  <body>
    <section><title>HSSF Use Cases</title>
    <section><title>Use Case 1: Read existing HSSF</title>

<p><strong>Primary Actor:</strong> HSSF client</p>
<p><strong>Scope:</strong> HSSF</p>
<p><strong>Level:</strong> Summary</p>
<p><strong>Stakeholders and Interests:</strong></p>
<ul>
	<li>HSSF client- wants to read content
	of HSSF file</li>
	<li>HSSF - understands HSSF file</li>
	<li>POIFS - understands underlying POI
	file system</li>
</ul>
<p><strong>Precondition:</strong> None</p>
<p><strong>Minimal Guarantee:</strong> None</p>
<p><strong>Main Success Guarantee:</strong></p>
<ol>
	<li>HSSF client requests HSSF to read
	a HSSF file, providing an InputStream
	containing HSSF file in question.</li>
	<li>HSSF requests POIFS to read the HSSF
	file, passing the InputStream
	object to POIFS (POIFS use case 1, read existing file system)</li>
	<li>HSSF reads the &quot;Workbook&quot;
	file (use case 4, read workbook entry)</li>
</ol>
<p><strong>Extensions:</strong></p>
<p>2a. Exceptions
thrown by POIFS will be passed on to the HSSF client.</p>
</section>
    <section><title>Use Case 2: Write HSSF file</title>

<p><strong>Primary Actor:</strong> HSSF client</p>
<p><strong>Scope:</strong> HSSF</p>
<p><strong>Level:</strong> Summary</p>
<p><strong>Stakeholders and Interests:</strong></p>
<ul>
	<li>HSSF client- wants to write file
	out.</li>
	<li>HSSF - knows how to write file
	out.</li>
	<li>POIFS - knows how to write file
	system out.</li>
</ul>
<p><strong>Precondition:</strong></p>
<ul>
	<li>File has been
	read (use case 1, read existing HSSF file) and subsequently modified
	or file has been created (use case 3, create HSSF file)</li>
</ul>
<p><strong>Minimal Guarantee:</strong> None</p>
<p><strong>Main Success Guarantee:</strong></p>
<ol>
	<li>HSSF client
	provides an OutputStream to
	write the file to.</li>
	<li>HSSF writes
	the &quot;Workbook&quot; to its associated POIFS file system (use case
	5, write workbook entry)</li>
	<li>HSSF
	requests POIFS to write its file system out, using the OutputStream
	obtained from the HSSF client (POIFS use case 2, write file system).</li>
</ol>
<p><strong>Extensions:</strong></p>
<p>3a. Exceptions
from POIFS are passed to the HSSF client.</p>

</section>
    <section><title>Use Case 3:Create HSSF file</title>

<p><strong>Primary Actor:</strong> HSSF client</p>
<p><strong>Scope:</strong> HSSF</p>
<p>
<strong>Level:</strong> Summary</p>
<p><strong>Stakeholders and Interests:</strong></p>
<ul>
	<li>HSSF client- wants to create a new
	file.</li>
	<li>HSSF - knows how to create a new
	file.</li>
	<li>POIFS - knows how to create a new
	file system.</li>
</ul>
<p><strong>Precondition:</strong></p>
<p><strong>Minimal Guarantee:</strong> None</p>
<p><strong>Main Success Guarantee:</strong></p>
<ol>
	<li>HSSF requests
	POIFS to create a new file system (POIFS use case 3, create new file
	system)</li>
</ol>
<p><strong>Extensions:</strong>
None</p>

</section>
    <section><title>Use Case 4: Read workbook entry</title>
<p><strong>Primary Actor:</strong> HSSF</p>
<p><strong>Scope:</strong> HSSF</p>
<p>
<strong>Level:</strong> Summary</p>
<p><strong>Stakeholders and Interests:</strong></p>
<ul>
	<li>HSSF - knows how to read the
	workbook entry</li>
	<li>POIFS - knows how to manage the file
	system.</li>
</ul>
<p><strong>Precondition:</strong></p>
<ul>
	<li>The file
	system has been read (use case 1, read existing HSSF file) or has
	been created and written to (use case 3, create HSSF file system;
	use case 5, write workbook entry).</li>
</ul>
<p><strong>Minimal
Guarantee:</strong> None</p>
<p><strong>Main Success Guarantee:</strong></p>
<ol>
	<li>
	HSSF requests POIFS for the &quot;Workbook&quot; file</li>
	<li>POIFS returns
	an InputStream for the file.</li>
	<li>HSSF reads
	from the InputStream provided by POIFS</li>
	<li>HSSF closes
	the InputStream provided by POIFS</li>
</ol>
<p><strong>Extensions:</strong></p>
<p>3a. Exceptions
thrown by POIFS will be passed on</p>
</section>
    <section><title>Use Case 5: Write workbook entry</title>


<p><strong>Primary Actor:</strong> HSSF</p>
<p><strong>Scope:</strong> HSSF</p>
<p>
<strong>Level:</strong> Summary</p>
<p><strong>Stakeholders and Interests:</strong></p>
<ul>
	<li>HSSF - knows how to manage the
	write the workbook entry.</li>
	<li>POIFS - knows how to manage the file
	system.</li>
</ul>
<p><strong>Precondition:</strong> 
</p>
<ul>
	<li>Either an existing HSSF file has
	been read (use case 1, read existing HSSF file) or an HSSF file has
	been created (use case 3, create HSSF file).</li>
</ul>
<p><strong>Minimal Guarantee:</strong> None</p>
<p><strong>Main Success Guarantee:</strong></p>
<ol>
	<li>HSSF
	checks the POIFS file system directory for the &quot;Workbook&quot;
	file (POIFS use case  8, read file system directory)</li>
	<li>If &quot;Workbook&quot; is in the directory, HSSF requests POIFS to
	replace it with the new workbook entry (POIFS use case 4, replace file
	in file system). Otherwise, HSSF requests POIFS to write the new
	workbook file, with the name &quot;Workbook&quot; (POIFS use case 6,
	write new file to file system)</li>
</ol>
<p><strong>Extensions:</strong>None</p>
</section>

</section>
</body>
</document>
