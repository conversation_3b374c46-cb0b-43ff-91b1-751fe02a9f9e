<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd">

<document>
    <header>
        <title>Chart record information</title>
        <authors>
            <person email="<EMAIL>" name="Glen Stampoultzis" id="GS"/>
        </authors>
    </header>
    <body>
        <section><title>Introduction</title>
            <p>
                This document is intended as a work in progress for describing
                our current understanding of how the chart records are
                written to produce a valid chart.
            </p>
        </section>
        <section><title>Bar chart</title>
            <p>
                The following records detail the records written for a
                'simple' bar chart.
            </p>
            <source>

    ============================================
    rectype = 0xec, recsize = 0xc8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 02 F0 C0 00 00 00 10 00 08 F0 08 00 00 00 ................
    00000010 02 00 00 00 02 04 00 00 0F 00 03 F0 A8 00 00 00 ................
    00000020 0F 00 04 F0 28 00 00 00 01 00 09 F0 10 00 00 00 ....(...........
    00000030 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000040 02 00 0A F0 08 00 00 00 00 04 00 00 05 00 00 00 ................
    00000050 0F 00 04 F0 70 00 00 00 92 0C 0A F0 08 00 00 00 ....p...........
    00000060 02 04 00 00 00 0A 00 00 93 00 0B F0 36 00 00 00 ............6...
    00000070 7F 00 04 01 04 01 BF 00 08 00 08 00 81 01 4E 00 ..............N.
    00000080 00 08 83 01 4D 00 00 08 BF 01 10 00 11 00 C0 01 ....M...........
    00000090 4D 00 00 08 FF 01 08 00 08 00 3F 02 00 00 02 00 M.........?.....
    000000A0 BF 03 00 00 08 00 00 00 10 F0 12 00 00 00 00 00 ................
    000000B0 04 00 C0 02 0A 00 F4 00 0E 00 66 01 20 00 E9 00 ..........f. ...
    000000C0 00 00 11 F0 00 00 00 00                         ........
    -END DUMP-----------------------------------
    recordid = 0xec, size =200
    [UNKNOWN RECORD:ec]
        .id        = ec
    [/UNKNOWN RECORD]

    ============================================
    rectype = 0x5d, recsize = 0x1a
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 12 00 05 00 02 00 11 60 00 00 00 00 B8 03 .........`......
    00000010 87 03 00 00 00 00 00 00 00 00                   ..........
    -END DUMP-----------------------------------
    recordid = 0x5d, size =26
    [UNKNOWN RECORD:5d]
        .id        = 5d
    [/UNKNOWN RECORD]

    ============================================
    rectype = 0x809, recsize = 0x10
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 20 00 FE 1C CD 07 C9 40 00 00 06 01 00 00 .. ......@......
    -END DUMP-----------------------------------
    recordid = 0x809, size =16
    [BOF RECORD]
        .version         = 600
        .type            = 20
        .build           = 1cfe
        .buildyear       = 1997
        .history         = 40c9
        .requiredversion = 106
    [/BOF RECORD]

    ============================================
    rectype = 0x14, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x14, size =0
    [HEADER]
        .length         = 0
        .header         = null
    [/HEADER]

    ============================================
    rectype = 0x15, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x15, size =0
    [FOOTER]
        .footerlen      = 0
        .footer         = null
    [/FOOTER]

    ============================================
    rectype = 0x83, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x83, size =2
    [HCENTER]
        .hcenter        = false
    [/HCENTER]

    ============================================
    rectype = 0x84, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x84, size =2
    [VCENTER]
        .vcenter        = false
    [/VCENTER]

    ============================================
    rectype = 0xa1, recsize = 0x22
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 12 00 01 00 01 00 01 00 04 00 00 00 B8 03 ................
    00000010 00 00 00 00 00 00 E0 3F 00 00 00 00 00 00 E0 3F .......?.......?
    00000020 0F 00                                           ..
    -END DUMP-----------------------------------
    recordid = 0xa1, size =34
    [PRINTSETUP]
        .papersize      = 0
        .scale          = 18
        .pagestart      = 1
        .fitwidth       = 1
        .fitheight      = 1
        .options        = 4
            .ltor       = false
            .landscape  = false
            .valid      = true
            .mono       = false
            .draft      = false
            .notes      = false
            .noOrientat = false
            .usepage    = false
        .hresolution    = 0
        .vresolution    = 952
        .headermargin   = 0.5
        .footermargin   = 0.5
        .copies         = 15
    [/PRINTSETUP]

	<!-- Comment to avoid forrest bug -->
    ============================================
    rectype = 0x33, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x33, size =2
    [UNKNOWN RECORD:33]
        .id        = 33
    [/UNKNOWN RECORD]

    ============================================
    rectype = 0x1060, recsize = 0xa
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 08 16 C8 00 00 00 05 00                   .#........
    -END DUMP-----------------------------------
    recordid = 0x1060, size =10
    [FBI]
        .xBasis               = 0x23A0 (9120 )
        .yBasis               = 0x1608 (5640 )
        .heightBasis          = 0x00C8 (200 )
        .scale                = 0x0000 (0 )
        .indexToFontTable     = 0x0005 (5 )
    [/FBI]

    ============================================
    rectype = 0x1060, recsize = 0xa
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 08 16 C8 00 01 00 06 00                   .#........
    -END DUMP-----------------------------------
    recordid = 0x1060, size =10
    [FBI]
        .xBasis               = 0x23A0 (9120 )
        .yBasis               = 0x1608 (5640 )
        .heightBasis          = 0x00C8 (200 )
        .scale                = 0x0001 (1 )
        .indexToFontTable     = 0x0006 (6 )
    [/FBI]

    ============================================
    rectype = 0x12, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x12, size =2
    [PROTECT]
        .rowheight      = 0
    [/PROTECT]

    ============================================
    Offset 0xf22 (3874)
    rectype = 0x1001, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1001, size =2
    [UNITS]
        .units                = 0x0000 (0 )
    [/UNITS]

    ============================================
    Offset 0xf28 (3880)
    rectype = 0x1002, recsize = 0x10
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00 58 66 D0 01 40 66 22 01 ........Xf..@f".
    -END DUMP-----------------------------------
    recordid = 0x1002, size =16
    [CHART]
        .x                    = 0x00000000 (0 )
        .y                    = 0x00000000 (0 )
        .width                = 0x01D06658 (30434904 )
        .height               = 0x01226640 (19031616 )
    [/CHART]

    ============================================
    Offset 0xf3c (3900)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0xf40 (3904)
    rectype = 0xa0, recsize = 0x4
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 01 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0xa0, size =4
    [SCL]
        .numerator            = 0x0001 (1 )
        .denominator          = 0x0001 (1 )
    [/SCL]

	<!-- Comment to avoid forrest bug -->
    ============================================
    Offset 0xf48 (3912)
    rectype = 0x1064, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 01 00 00 00 01 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1064, size =8
    [PLOTGROWTH]
        .horizontalScale      = 0x00010000 (65536 )
        .verticalScale        = 0x00010000 (65536 )
    [/PLOTGROWTH]

    ============================================
    Offset 0xf54 (3924)
    rectype = 0x1032, recsize = 0x4
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 02 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x1032, size =4
    [FRAME]
        .borderType           = 0x0000 (0 )
        .options              = 0x0002 (2 )
             .autoSize                 = false
             .autoPosition             = true
    [/FRAME]

    ============================================
    Offset 0xf5c (3932)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0xf60 (3936)
    rectype = 0x1007, recsize = 0xc
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 FF FF 09 00 4D 00             ..........M.
    -END DUMP-----------------------------------
    recordid = 0x1007, size =12
    [LINEFORMAT]
        .lineColor            = 0x00000000 (0 )
        .linePattern          = 0x0000 (0 )
        .weight               = 0xFFFF (-1 )
        .format               = 0x0009 (9 )
             .auto                     = true
             .drawTicks                = false
             .unknown                  = false
        .colourPaletteIndex   = 0x004D (77 )
    [/LINEFORMAT]

    ============================================
    Offset 0xf70 (3952)
    rectype = 0x100a, recsize = 0x10
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> FF 00 00 00 00 00 01 00 01 00 4E 00 4D 00 ............N.M.
    -END DUMP-----------------------------------
    recordid = 0x100a, size =16
    [AREAFORMAT]
        .foregroundColor      = 0x00FFFFFF (16777215 )
        .backgroundColor      = 0x00000000 (0 )
        .pattern              = 0x0001 (1 )
        .formatFlags          = 0x0001 (1 )
             .automatic                = true
             .invert                   = false
        .forecolorIndex       = 0x004E (78 )
        .backcolorIndex       = 0x004D (77 )
    [/AREAFORMAT]

    ============================================
    Offset 0xf84 (3972)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0xf88 (3976)
    rectype = 0x1003, recsize = 0xc
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 01 00 20 00 1F 00 01 00 00 00             .... .......
    -END DUMP-----------------------------------
    recordid = 0x1003, size =12
    [SERIES]
        .categoryDataType     = 0x0001 (1 )
        .valuesDataType       = 0x0001 (1 )
        .numCategories        = 0x0020 (32 )
        .numValues            = 0x001F (31 )
        .bubbleSeriesType     = 0x0001 (1 )
        .numBubbleValues      = 0x0000 (0 )
    [/SERIES]

    ============================================
    Offset 0xf98 (3992)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

	<!-- Comment to avoid forrest bug -->
    ============================================
    Offset 0xf9c (3996)
    rectype = 0x1051, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1051, size =8
    [AI]
        .linkType             = 0x00 (0 )
        .referenceType        = 0x01 (1 )
        .options              = 0x0000 (0 )
             .customNumberFormat       = false
        .indexNumberFmtRecord = 0x0000 (0 )
        .formulaOfLink        =  (org.apache.poi.hssf.record.LinkedDataFormulaField@1ee3914 )
    [/AI]

    ============================================
    Offset 0xfa8 (4008)
    rectype = 0x1051, recsize = 0x13
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 0B 00 3B 00 00 00 00 1E 00 01 ........;.......
    00000010 00 01 00                                        ...
    -END DUMP-----------------------------------
    recordid = 0x1051, size =19
    [AI]
        .linkType             = 0x01 (1 )
        .referenceType        = 0x02 (2 )
        .options              = 0x0000 (0 )
             .customNumberFormat       = false
        .indexNumberFmtRecord = 0x0000 (0 )
        .formulaOfLink        =  (org.apache.poi.hssf.record.LinkedDataFormulaField@e5855a )
    [/AI]

    ============================================
    Offset 0xfbf (4031)
    rectype = 0x1051, recsize = 0x13
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 69 01 0B 00 3B 00 00 00 00 1F 00 00 ....i...;.......
    00000010 00 00 00                                        ...
    -END DUMP-----------------------------------
    recordid = 0x1051, size =19
    [AI]
        .linkType             = 0x02 (2 )
        .referenceType        = 0x02 (2 )
        .options              = 0x0000 (0 )
             .customNumberFormat       = false
        .indexNumberFmtRecord = 0x0169 (361 )
        .formulaOfLink        =  (org.apache.poi.hssf.record.LinkedDataFormulaField@95fd19 )
    [/AI]

    ============================================
    Offset 0xfd6 (4054)
    rectype = 0x1051, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1051, size =8
    [AI]
        .linkType             = 0x03 (3 )
        .referenceType        = 0x01 (1 )
        .options              = 0x0000 (0 )
             .customNumberFormat       = false
        .indexNumberFmtRecord = 0x0000 (0 )
        .formulaOfLink        =  (org.apache.poi.hssf.record.LinkedDataFormulaField@11b9fb1 )
    [/AI]

    ============================================
    Offset 0xfe2 (4066)
    rectype = 0x1006, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1006, size =8
    [DATAFORMAT]
        .pointNumber          = 0xFFFF (-1 )
        .seriesIndex          = 0x0000 (0 )
        .seriesNumber         = 0x0000 (0 )
        .formatFlags          = 0x0000 (0 )
             .useExcel4Colors          = false
    [/DATAFORMAT]

    ============================================
    Offset 0xfee (4078)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0xff2 (4082)
    rectype = 0x105f, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x105f, size =2
    [UNKNOWN RECORD]
        .id        = 105f
    [/UNKNOWN RECORD]

    ============================================
    Offset 0xff8 (4088)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0xffc (4092)
    rectype = 0x1045, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1045, size =2
    [SeriesToChartGroup]
        .chartGroupIndex      = 0x0000 (0 )
    [/SeriesToChartGroup]

    ============================================
    Offset 0x1002 (4098)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x1006 (4102)
    rectype = 0x1044, recsize = 0x4
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x1044, size =4
    [SHTPROPS]
        .flags                = 0x000A (10 )
             .chartTypeManuallyFormatted     = false
             .plotVisibleOnly          = true
             .doNotSizeWithWindow      = false
             .defaultPlotDimensions     = true
             .autoPlotArea             = false
        .empty                = 0x00 (0 )
    [/SHTPROPS]

    ============================================
    Offset 0x100e (4110)
    rectype = 0x1024, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1024, size =2
    [DEFAULTTEXT]
        .categoryDataType     = 0x0002 (2 )
    [/DEFAULTTEXT]

    ============================================
    Offset 0x1014 (4116)
    rectype = 0x1025, recsize = 0x20
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 01 00 00 00 00 00 DB FF FF FF C4 FF FF FF ................
    00000010 00 00 00 00 00 00 00 00 B1 00 4D 00 50 2B 00 00 ..........M.P+..
    -END DUMP-----------------------------------
    recordid = 0x1025, size =32
    [TEXT]
        .horizontalAlignment  = 0x02 (2 )
        .verticalAlignment    = 0x02 (2 )
        .displayMode          = 0x0001 (1 )
        .rgbColor             = 0x00000000 (0 )
        .x                    = 0xFFFFFFDB (-37 )
        .y                    = 0xFFFFFFC4 (-60 )
        .width                = 0x00000000 (0 )
        .height               = 0x00000000 (0 )
        .options1             = 0x00B1 (177 )
             .autoColor                = true
             .showKey                  = false
             .showValue                = false
             .vertical                 = false
             .autoGeneratedText        = true
             .generated                = true
             .autoLabelDeleted         = false
             .autoBackground           = true
             .rotation                 = 0
             .showCategoryLabelAsPercentage     = false
             .showValueAsPercentage     = false
             .showBubbleSizes          = false
             .showLabel                = false
        .indexOfColorValue    = 0x004D (77 )
        .options2             = 0x2B50 (11088 )
             .dataLabelPlacement       = 0
        .textRotation         = 0x0000 (0 )
    [/TEXT]

    ============================================
    Offset 0x1038 (4152)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

	<!-- Comment to avoid forrest bug -->
    ============================================
    Offset 0x103c (4156)
    rectype = 0x104f, recsize = 0x14
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 02 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00 00 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x104f, size =20
    [UNKNOWN RECORD]
        .id        = 104f
    [/UNKNOWN RECORD]

    ============================================
    Offset 0x1054 (4180)
    rectype = 0x1026, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1026, size =2
    [FONTX]
        .fontIndex            = 0x0005 (5 )
    [/FONTX]

    ============================================
    Offset 0x105a (4186)
    rectype = 0x1051, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1051, size =8
    [AI]
        .linkType             = 0x00 (0 )
        .referenceType        = 0x01 (1 )
        .options              = 0x0000 (0 )
             .customNumberFormat       = false
        .indexNumberFmtRecord = 0x0000 (0 )
        .formulaOfLink        =  (org.apache.poi.hssf.record.LinkedDataFormulaField@913fe2 )
    [/AI]

    ============================================
    Offset 0x1066 (4198)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x106a (4202)
    rectype = 0x1024, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1024, size =2
    [DEFAULTTEXT]
        .categoryDataType     = 0x0003 (3 )
    [/DEFAULTTEXT]

    ============================================
    Offset 0x1070 (4208)
    rectype = 0x1025, recsize = 0x20
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 01 00 00 00 00 00 DB FF FF FF C4 FF FF FF ................
    00000010 00 00 00 00 00 00 00 00 B1 00 4D 00 50 2B 00 00 ..........M.P+..
    -END DUMP-----------------------------------
    recordid = 0x1025, size =32
    [TEXT]
        .horizontalAlignment  = 0x02 (2 )
        .verticalAlignment    = 0x02 (2 )
        .displayMode          = 0x0001 (1 )
        .rgbColor             = 0x00000000 (0 )
        .x                    = 0xFFFFFFDB (-37 )
        .y                    = 0xFFFFFFC4 (-60 )
        .width                = 0x00000000 (0 )
        .height               = 0x00000000 (0 )
        .options1             = 0x00B1 (177 )
             .autoColor                = true
             .showKey                  = false
             .showValue                = false
             .vertical                 = false
             .autoGeneratedText        = true
             .generated                = true
             .autoLabelDeleted         = false
             .autoBackground           = true
             .rotation                 = 0
             .showCategoryLabelAsPercentage     = false
             .showValueAsPercentage     = false
             .showBubbleSizes          = false
             .showLabel                = false
        .indexOfColorValue    = 0x004D (77 )
        .options2             = 0x2B50 (11088 )
             .dataLabelPlacement       = 0
        .textRotation         = 0x0000 (0 )
    [/TEXT]

    ============================================
    Offset 0x1094 (4244)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x1098 (4248)
    rectype = 0x104f, recsize = 0x14
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 02 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00 00 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x104f, size =20
    [UNKNOWN RECORD]
        .id        = 104f
    [/UNKNOWN RECORD]

    ============================================
    Offset 0x10b0 (4272)
    rectype = 0x1026, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1026, size =2
    [FONTX]
        .fontIndex            = 0x0006 (6 )
    [/FONTX]

    ============================================
    Offset 0x10b6 (4278)
    rectype = 0x1051, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1051, size =8
    [AI]
        .linkType             = 0x00 (0 )
        .referenceType        = 0x01 (1 )
        .options              = 0x0000 (0 )
             .customNumberFormat       = false
        .indexNumberFmtRecord = 0x0000 (0 )
        .formulaOfLink        =  (org.apache.poi.hssf.record.LinkedDataFormulaField@1f934ad )
    [/AI]

    ============================================
    Offset 0x10c2 (4290)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x10c6 (4294)
    rectype = 0x1046, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1046, size =2
    [AXISUSED]
        .numAxis              = 0x0001 (1 )
    [/AXISUSED]

    ============================================
    Offset 0x10cc (4300)
    rectype = 0x1041, recsize = 0x12
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> DF 01 00 00 DD 00 00 00 B3 0B 00 00 56 0B ..............V.
    00000010 00 00                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1041, size =18
    [AXISPARENT]
        .axisType             = 0x0000 (0 )
        .x                    = 0x000001DF (479 )
        .y                    = 0x000000DD (221 )
        .width                = 0x00000BB3 (2995 )
        .height               = 0x00000B56 (2902 )
    [/AXISPARENT]

    ============================================
    Offset 0x10e2 (4322)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x10e6 (4326)
    rectype = 0x104f, recsize = 0x14
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 02 00 3A 00 00 00 5E 00 00 00 58 0D 00 00 ....:...^...X...
    00000010 E5 0E 00 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x104f, size =20
    [UNKNOWN RECORD]
        .id        = 104f
    [/UNKNOWN RECORD]

    ============================================
    Offset 0x10fe (4350)
    rectype = 0x101d, recsize = 0x12
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00                                           ..
    -END DUMP-----------------------------------
    recordid = 0x101d, size =18
    [AXIS]
        .axisType             = 0x0000 (0 )
        .reserved1            = 0x00000000 (0 )
        .reserved2            = 0x00000000 (0 )
        .reserved3            = 0x00000000 (0 )
        .reserved4            = 0x00000000 (0 )
    [/AXIS]

    ============================================
    Offset 0x1114 (4372)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x1118 (4376)
    rectype = 0x1020, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 01 00 01 00 01 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1020, size =8
    [CATSERRANGE]
        .crossingPoint        = 0x0001 (1 )
        .labelFrequency       = 0x0001 (1 )
        .tickMarkFrequency    = 0x0001 (1 )
        .options              = 0x0001 (1 )
             .valueAxisCrossing        = true
             .crossesFarRight          = false
             .reversed                 = false
    [/CATSERRANGE]

    ============================================
    Offset 0x1124 (4388)
    rectype = 0x1062, recsize = 0x12
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 39 90 02 00 00 00 01 00 00 00 00 00 1C 90 ..9.............
    00000010 FF 00                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1062, size =18
    [AXCEXT]
        .minimumCategory      = 0x901C (-28644 )
        .maximumCategory      = 0x9039 (-28615 )
        .majorUnitValue       = 0x0002 (2 )
        .majorUnit            = 0x0000 (0 )
        .minorUnitValue       = 0x0001 (1 )
        .minorUnit            = 0x0000 (0 )
        .baseUnit             = 0x0000 (0 )
        .crossingPoint        = 0x901C (-28644 )
        .options              = 0x00FF (255 )
             .defaultMinimum           = true
             .defaultMaximum           = true
             .defaultMajor             = true
             .defaultMinorUnit         = true
             .isDate                   = true
             .defaultBase              = true
             .defaultCross             = true
             .defaultDateSettings      = true
    [/AXCEXT]

    ============================================
    Offset 0x113a (4410)
    rectype = 0x101e, recsize = 0x1e
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 03 01 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00 00 00 00 00 00 00 23 00 4D 00 2D 00       ........#.M.-.
    -END DUMP-----------------------------------
    recordid = 0x101e, size =30
    [TICK]
        .majorTickType        = 0x02 (2 )
        .minorTickType        = 0x00 (0 )
        .labelPosition        = 0x03 (3 )
        .background           = 0x01 (1 )
        .labelColorRgb        = 0x00000000 (0 )
        .zero1                = 0x0000 (0 )
        .zero2                = 0x0000 (0 )
        .options              = 0x0023 (35 )
             .autoTextColor            = true
             .autoTextBackground       = true
             .rotation                 = 0
             .autorotate               = true
        .tickColor            = 0x004D (77 )
        .zero3                = 0x002D (45 )
    [/TICK]

    ============================================
    Offset 0x115c (4444)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x1160 (4448)
    rectype = 0x101d, recsize = 0x12
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00                                           ..
    -END DUMP-----------------------------------
    recordid = 0x101d, size =18
    [AXIS]
        .axisType             = 0x0001 (1 )
        .reserved1            = 0x00000000 (0 )
        .reserved2            = 0x00000000 (0 )
        .reserved3            = 0x00000000 (0 )
        .reserved4            = 0x00000000 (0 )
    [/AXIS]

	<!-- Comment to avoid forrest bug -->
    ============================================
    Offset 0x1176 (4470)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x117a (4474)
    rectype = 0x101f, recsize = 0x2a
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000020 00 00 00 00 00 00 00 00 1F 01                   ..........
    -END DUMP-----------------------------------
    recordid = 0x101f, size =42
    [VALUERANGE]
        .minimumAxisValue     =  (0.0 )
        .maximumAxisValue     =  (0.0 )
        .majorIncrement       =  (0.0 )
        .minorIncrement       =  (0.0 )
        .categoryAxisCross    =  (0.0 )
        .options              = 0x011F (287 )
             .automaticMinimum         = true
             .automaticMaximum         = true
             .automaticMajor           = true
             .automaticMinor           = true
             .automaticCategoryCrossing     = true
             .logarithmicScale         = false
             .valuesInReverse          = false
             .crossCategoryAxisAtMaximum     = false
             .reserved                 = true
    [/VALUERANGE]

    ============================================
    Offset 0x11a8 (4520)
    rectype = 0x101e, recsize = 0x1e
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 03 01 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00 00 00 00 00 00 00 23 00 4D 00 00 00       ........#.M...
    -END DUMP-----------------------------------
    recordid = 0x101e, size =30
    [TICK]
        .majorTickType        = 0x02 (2 )
        .minorTickType        = 0x00 (0 )
        .labelPosition        = 0x03 (3 )
        .background           = 0x01 (1 )
        .labelColorRgb        = 0x00000000 (0 )
        .zero1                = 0x0000 (0 )
        .zero2                = 0x0000 (0 )
        .options              = 0x0023 (35 )
             .autoTextColor            = true
             .autoTextBackground       = true
             .rotation                 = 0
             .autorotate               = true
        .tickColor            = 0x004D (77 )
        .zero3                = 0x0000 (0 )
    [/TICK]

    ============================================
    Offset 0x11ca (4554)
    rectype = 0x1021, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1021, size =2
    [AXISLINEFORMAT]
        .axisType             = 0x0001 (1 )
    [/AXISLINEFORMAT]

    ============================================
    Offset 0x11d0 (4560)
    rectype = 0x1007, recsize = 0xc
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 FF FF 09 00 4D 00             ..........M.
    -END DUMP-----------------------------------
    recordid = 0x1007, size =12
    [LINEFORMAT]
        .lineColor            = 0x00000000 (0 )
        .linePattern          = 0x0000 (0 )
        .weight               = 0xFFFF (-1 )
        .format               = 0x0009 (9 )
             .auto                     = true
             .drawTicks                = false
             .unknown                  = false
        .colourPaletteIndex   = 0x004D (77 )
    [/LINEFORMAT]

    ============================================
    Offset 0x11e0 (4576)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x11e4 (4580)
    rectype = 0x1035, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1035, size =0
    [PLOTAREA]
    [/PLOTAREA]

    ============================================
    Offset 0x11e8 (4584)
    rectype = 0x1032, recsize = 0x4
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 03 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x1032, size =4
    [FRAME]
        .borderType           = 0x0000 (0 )
        .options              = 0x0003 (3 )
             .autoSize                 = true
             .autoPosition             = true
    [/FRAME]

    ============================================
    Offset 0x11f0 (4592)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x11f4 (4596)
    rectype = 0x1007, recsize = 0xc
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 80 00 00 00 00 00 00 00 17 00             ............
    -END DUMP-----------------------------------
    recordid = 0x1007, size =12
    [LINEFORMAT]
        .lineColor            = 0x00808080 (8421504 )
        .linePattern          = 0x0000 (0 )
        .weight               = 0x0000 (0 )
        .format               = 0x0000 (0 )
             .auto                     = false
             .drawTicks                = false
             .unknown                  = false
        .colourPaletteIndex   = 0x0017 (23 )
    [/LINEFORMAT]

    ============================================
    Offset 0x1204 (4612)
    rectype = 0x100a, recsize = 0x10
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> C0 00 00 00 00 00 01 00 00 00 16 00 4F 00 ..............O.
    -END DUMP-----------------------------------
    recordid = 0x100a, size =16
    [AREAFORMAT]
        .foregroundColor      = 0x00C0C0C0 (12632256 )
        .backgroundColor      = 0x00000000 (0 )
        .pattern              = 0x0001 (1 )
        .formatFlags          = 0x0000 (0 )
             .automatic                = false
             .invert                   = false
        .forecolorIndex       = 0x0016 (22 )
        .backcolorIndex       = 0x004F (79 )
    [/AREAFORMAT]

    ============================================
    Offset 0x1218 (4632)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x121c (4636)
    rectype = 0x1014, recsize = 0x14
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00 00 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x1014, size =20
    [CHARTFORMAT]
        .xPosition       = 0
        .yPosition       = 0
        .width           = 0
        .height          = 0
        .grBit           = 0
    [/CHARTFORMAT]

    ============================================
    Offset 0x1234 (4660)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x1238 (4664)
    rectype = 0x1017, recsize = 0x6
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 96 00 00 00                               ......
    -END DUMP-----------------------------------
    recordid = 0x1017, size =6
    [BAR]
        .barSpace             = 0x0000 (0 )
        .categorySpace        = 0x0096 (150 )
        .formatFlags          = 0x0000 (0 )
             .horizontal               = false
             .stacked                  = false
             .displayAsPercentage      = false
             .shadow                   = false
    [/BAR]

    ============================================
    Offset 0x1242 (4674)
    rectype = 0x1022, recsize = 0xa
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00 0F 00                   ..........
    -END DUMP-----------------------------------
    recordid = 0x1022, size =10
    [UNKNOWN RECORD]
        .id        = 1022
    [/UNKNOWN RECORD]

    ============================================
    Offset 0x1250 (4688)
    rectype = 0x1015, recsize = 0x14
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 1E 06 00 00 B5 01 00 00 D5 00 00 00 ................
    00000010 03 01 1F 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x1015, size =20
    [LEGEND]
        .xAxisUpperLeft       = 0x00000DD6 (3542 )
        .yAxisUpperLeft       = 0x0000061E (1566 )
        .xSize                = 0x000001B5 (437 )
        .ySize                = 0x000000D5 (213 )
        .type                 = 0x03 (3 )
        .spacing              = 0x01 (1 )
        .options              = 0x001F (31 )
             .autoPosition             = true
             .autoSeries               = true
             .autoXPositioning         = true
             .autoYPositioning         = true
             .vertical                 = true
             .dataTable                = false
    [/LEGEND]

    ============================================
    Offset 0x1268 (4712)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x126c (4716)
    rectype = 0x104f, recsize = 0x14
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 02 00 D6 0D 00 00 1E 06 00 00 00 00 00 00 ................
    00000010 00 00 00 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x104f, size =20
    [UNKNOWN RECORD]
        .id        = 104f
    [/UNKNOWN RECORD]

    ============================================
    Offset 0x1284 (4740)
    rectype = 0x1025, recsize = 0x20
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 01 00 00 00 00 00 DB FF FF FF C4 FF FF FF ................
    00000010 00 00 00 00 00 00 00 00 B1 00 4D 00 70 37 00 00 ..........M.p7..
    -END DUMP-----------------------------------
    recordid = 0x1025, size =32
    [TEXT]
        .horizontalAlignment  = 0x02 (2 )
        .verticalAlignment    = 0x02 (2 )
        .displayMode          = 0x0001 (1 )
        .rgbColor             = 0x00000000 (0 )
        .x                    = 0xFFFFFFDB (-37 )
        .y                    = 0xFFFFFFC4 (-60 )
        .width                = 0x00000000 (0 )
        .height               = 0x00000000 (0 )
        .options1             = 0x00B1 (177 )
             .autoColor                = true
             .showKey                  = false
             .showValue                = false
             .vertical                 = false
             .autoGeneratedText        = true
             .generated                = true
             .autoLabelDeleted         = false
             .autoBackground           = true
             .rotation                 = 0
             .showCategoryLabelAsPercentage     = false
             .showValueAsPercentage     = false
             .showBubbleSizes          = false
             .showLabel                = false
        .indexOfColorValue    = 0x004D (77 )
        .options2             = 0x3770 (14192 )
             .dataLabelPlacement       = 0
        .textRotation         = 0x0000 (0 )
    [/TEXT]

    ============================================
    Offset 0x12a8 (4776)
    rectype = 0x1033, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1033, size =0
    [BEGIN]
    [/BEGIN]

    ============================================
    Offset 0x12ac (4780)
    rectype = 0x104f, recsize = 0x14
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 02 00 00 00 00 00 00 00 00 00 00 00 00 00 ................
    00000010 00 00 00 00                                     ....
    -END DUMP-----------------------------------
    recordid = 0x104f, size =20
    [UNKNOWN RECORD]
        .id        = 104f
    [/UNKNOWN RECORD]

    ============================================
    Offset 0x12c4 (4804)
    rectype = 0x1051, recsize = 0x8
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 00 00 00 00                         ........
    -END DUMP-----------------------------------
    recordid = 0x1051, size =8
    [AI]
        .linkType             = 0x00 (0 )
        .referenceType        = 0x01 (1 )
        .options              = 0x0000 (0 )
             .customNumberFormat       = false
        .indexNumberFmtRecord = 0x0000 (0 )
        .formulaOfLink        =  (org.apache.poi.hssf.record.LinkedDataFormulaField@1d05c81 )
    [/AI]

    ============================================
    Offset 0x12d0 (4816)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

	<!-- Comment to avoid forrest bug -->
    ============================================
    Offset 0x12d4 (4820)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x12d8 (4824)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x12dc (4828)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    Offset 0x12e0 (4832)
    rectype = 0x1034, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0x1034, size =0
    [END]
    [/END]

    ============================================
    rectype = 0x200, recsize = 0xe
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 00 1F 00 00 00 00 00 01 00 00 00       ..............
    -END DUMP-----------------------------------
    recordid = 0x200, size =14
    [DIMENSIONS]
        .firstrow       = 0
        .lastrow        = 1f
        .firstcol       = 0
        .lastcol        = 1
        .zero           = 0
    [/DIMENSIONS]

    ============================================
    rectype = 0x1065, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1065, size =2
    [SINDEX]
        .index                = 0x0002 (2 )
    [/SINDEX]

    ============================================
    rectype = 0x1065, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1065, size =2
    [SINDEX]
        .index                = 0x0001 (1 )
    [/SINDEX]

    ============================================
    rectype = 0x1065, recsize = 0x2
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP>                                           ..
    -END DUMP-----------------------------------
    recordid = 0x1065, size =2
    [SINDEX]
        .index                = 0x0003 (3 )
    [/SINDEX]

    ============================================
    rectype = 0xa, recsize = 0x0
    -BEGIN DUMP---------------------------------
    **NO RECORD DATA**
    -END DUMP-----------------------------------
    recordid = 0xa, size =0
    [EOF]
    [/EOF]


            </source>
            <p>
                The next section breaks those records down into an easier
                to read format:
            </p>
            <source>
[UNKNOWN RECORD:ec]
[UNKNOWN RECORD:5d]
[BOF RECORD]
    [HEADER]
    [FOOTER]
    [HCENTER]
    [VCENTER]
    [PRINTSETUP]
    [UNKNOWN RECORD:33]
    [FBI]
    [FBI]
    [PROTECT]
    [UNITS]
    [CHART]
    [BEGIN]
        [SCL]                 // zoom magnification
        [PLOTGROWTH]          // font scaling
        [FRAME]               // border around text
        [BEGIN]               // default line and area format
            [LINEFORMAT]
            [AREAFORMAT]
        [END]
        [SERIES]              // start of series
        [BEGIN]
            [AI]              // LINK_TYPE_TITLE_OR_TEXT
            [AI]              // LINK_TYPE_VALUES
            [AI]              // LINK_TYPE_CATEGORIES
            [AI]              // ??
            [DATAFORMAT]      // Formatting applies to series?
            [BEGIN]           // ??
                [UNKNOWN RECORD]
            [END]
            [SeriesToChartGroup] // Used to support > 1 chart?
        [END]
        [SHTPROPS]            // Some defaults for how chart is displayed.
        [DEFAULTTEXT]         // Describes the characteristics of the next
                              // record
        [TEXT]                // Details of the text that follows in the
                              // next section
        [BEGIN]
            [UNKNOWN RECORD]  // POS record... looks like I missed this one.
                              // docs seem to indicate it's better to use
                              // defaults...
            [FONTX]           // index to font record.
            [AI]              // link to text?  seems to be linking to nothing
        [END]
        [DEFAULTTEXT]         // contains a category type of 3 which is not
                              // documented (sigh).
        [TEXT]                // defines position, color etc for text on chart.
        [BEGIN]
            [UNKNOWN RECORD]  // Another pos record
            [FONTX]           // font
            [AI]              // reference type is DIRECT (not sure what this
                              // means)
        [END]
        [AXISUSED]            // number of axis on the chart.
        [AXISPARENT]          // axis size and location
        [BEGIN]               // beginning of axis details
            [UNKNOWN RECORD]        // Another pos record.
            [AXIS]                  // Category axis
            [BEGIN]
                [CATSERRANGE]       // defines tick marks and other stuff
                [AXCEXT]            // unit information
                [TICK]              // tick formating characteristics
            [END]
            [AXIS]                  // Value axis
            [BEGIN]
                [VALUERANGE]        // defines tick marks and other stuff
                [TICK]              // tick formating characteristics
                [AXISLINEFORMAT]    // major grid line axis format
                [LINEFORMAT]        // what do the lines look like?
            [END]
            [PLOTAREA]              // marks that the frame following belongs
                                    // to the frame.
            [FRAME]                 // border
            [BEGIN]
                [LINEFORMAT]        // border line
                [AREAFORMAT]        // border area
            [END]
            [CHARTFORMAT]           // marks a chart group
            [BEGIN]
                [BAR]               // indicates a bar chart
                [UNKNOWN RECORD]    // apparently this record is ignoreable
                [LEGEND]            // positioning for the legend
                [BEGIN]
                    [UNKNOWN RECORD]    // another position record.
                    [TEXT]              // details of the text that follows
                                        // in the next section
                    [BEGIN]
                        [UNKNOWN RECORD]  // yet another pos record
                        [AI]              // another link (of type direct)
                    [END]
                [END]
            [END]
        [END]
    [END]
    [DIMENSIONS]
    [SINDEX]
    [SINDEX]
    [SINDEX]
[EOF]
            </source>
            <p>
                Just a quick note on some of the unknown records:
            </p>
            <ul>
                <li>EC: MSODRAWING - A Microsoft drawing record. (Need to
                track down where this is documented).</li>
                <li>5D: OBJ: Description of a drawing object. (This is going to
                be a PITA to implement).</li>
                <li>33: Not documented. :-(</li>
                <li>105f: Not documented.  :-(</li>
                <li>104f: POS: Position record (should be able to safely leave this out).</li>
                <li>1022: CHARTFORMATLINK: Can be left out.</li>
            </ul>
            <p>
                It is currently suspected that many of those records could be
                left out when generating a bar chart from scratch.  The way
                we will be proceeding with this is to write code that generates
                most of these records and then start removing them to see
                how this effects the chart in excel.
            </p>
        </section>
        <section><title>Inserting the Chart into the Workbook</title>
            <ul>
                <li>
                    Unknown record (sid=00eb) is inserted before the SST
                    record.
                </li>
            </ul>
                <source>
    ============================================
    rectype = 0xeb, recsize = 0x5a
    -BEGIN DUMP---------------------------------
    00000000 <USER> <GROUP> 00 F0 52 00 00 00 00 00 06 F0 18 00 00 00 ....R...........
    00000010 01 08 00 00 02 00 00 00 02 00 00 00 01 00 00 00 ................
    00000020 01 00 00 00 03 00 00 00 33 00 0B F0 12 00 00 00 ........3.......
    00000030 BF 00 08 00 08 00 81 01 09 00 00 08 C0 01 40 00 ..............@.
    00000040 00 08 40 00 1E F1 10 00 00 00 0D 00 00 08 0C 00 ..@.............
    00000050 00 08 17 00 00 08 F7 00 00 10                   ..........
    -END DUMP-----------------------------------
    recordid = 0xeb, size =90
    [UNKNOWN RECORD:eb]
        .id        = eb
    [/UNKNOWN RECORD]

    ============================================
                </source>
            <ul>
                <li>
                    Any extra font records are inserted as needed
                </li>
                <li>
                    Chart records inserted after DBCell records.
                </li>
            </ul>
        </section>
    </body>
</document>
