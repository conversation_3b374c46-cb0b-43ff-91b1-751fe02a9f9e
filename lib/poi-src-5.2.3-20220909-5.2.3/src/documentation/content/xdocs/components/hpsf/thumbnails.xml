<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd">

<document>
 <header>
  <title>HPSF THUMBNAIL HOW-TO</title>
  <authors>
   <person name="Drew Varner" email="<EMAIL>" />
  </authors>
 </header>
 <body>
  <section><title>The VT_CF Format</title>

   <p>Thumbnail information is stored as a VT_CF, or Thumbnail Variant. The
    Thumbnail Variant is used to store various types of information in a
    clipboard. The VT_CF can store information in formats for the Macintosh or
    Windows clipboard.</p>

   <p>There are many types of data that can be copied to the clipboard, but the
    only types of information needed for thumbnail manipulation are the image
    formats.</p>

   <p>The <code>VT_CF</code> structure looks like this:</p>

   <table>
    <tr>
     <th>Element:</th>
     <td>Clipboard Size</td>
     <td>Clipboard Format Tag</td>
     <td>Clipboard Data</td>
    </tr>
    <tr>
     <th>Size:</th>
     <td>32 bit unsigned integer (DWord)</td>
     <td>32 bit signed integer (DWord)</td>
     <td>variable length (byte array)</td>
    </tr>
   </table>

   <p>The Clipboard Size refers to the size (in bytes) of Clipboard Data
    (variable size) plus the Clipboard Format (four bytes).</p>

   <p>Clipboard Format Tag has four possible values:</p>

   <table>
    <tr>
     <th>Value</th>
     <th>Identifier</th>
     <th>Description</th>
    </tr>
    <tr>
     <td><code>-1L</code></td>
     <td><code>CFTAG_WINDOWS</code></td>
     <td>a built-in Windows&copy; clipboard format value</td>
    </tr>
    <tr>
     <td><code>-2L</code></td>
     <td><code>CFTAG_MACINTOSH</code></td>
     <td>a Macintosh clipboard format value</td>
    </tr>
    <tr>
     <td><code>-3L</code></td>
     <td><code>CFTAG_FMTID</code></td>
     <td>a format identifier (FMTID) This is rarely used.</td>
    </tr>
    <tr>
     <td><code>0L</code></td>
     <td><code>CFTAG_NODATA</code></td>
     <td>No data This is rarely used.</td>
    </tr>
   </table>
  </section>



  <section><title>Windows Clipboard Data</title>

   <p>Windows clipboard data has four image formats for thumbnails:</p>

   <table>
    <tr>
     <th>Value</th>
     <th>Identifier</th>
     <th>Description</th>
    </tr>
    <tr>
     <td>3</td>
     <td><code>CF_METAFILEPICT</code></td>
     <td>Windows metafile format - recommended</td>
    </tr>
    <tr>
     <td>8</td>
     <td><code>CF_DIB</code></td>
     <td>Device Independent Bitmap</td>
    </tr>
    <tr>
     <td>14</td>
     <td><code>CF_ENHMETAFILE</code></td>
     <td>Enhanced Windows metafile format</td>
    </tr>
    <tr>
     <td>2</td>
     <td><code>CF_BITMAP</code></td>
     <td>Bitmap - Obsolete - Use <code>CF_DIB</code> instead</td>
    </tr>
   </table>
  </section>

  <section><title>Windows Metafile Format</title>

   <p>The most common format for thumbnails on the Windows platform is the
    Windows metafile format. The Clipboard places and extra header in front of
    a the standard Windows Metafile Format data.</p>

   <p>The Clipboard Data byte array looks like this when an image is stored in
    Windows' Clipboard WMF format.</p>

   <table>
    <tr>
     <th>Identifier</th>
     <td>CF_METAFILEPICT</td>
     <td>mm</td>
     <td>width</td>
     <td>height</td>
     <td>handle</td>
     <td>WMF data</td>
    </tr>
    <tr>
     <th>Size</th>
     <td>32 bit unsigned int</td>
     <td>16 bit unsigned(?) int</td>
     <td>16 bit unsigned(?) int</td>
     <td>16 bit unsigned(?) int</td>
     <td>16 bit unsigned(?) int</td>
     <td>byte array - variable length</td>
    </tr>
    <tr>
     <th>Description</th>
     <td>Clipboard WMF</td>
     <td>Mapping Mode</td>
     <td>Image Width</td>
     <td>Image Height</td>
     <td>handle to the WMF data array in memory, or 0</td>
     <td>standard WMF byte stream</td>
    </tr>
   </table>
  </section>


  <section><title>Device Independent Bitmap</title>
   <p><strong>FIXME:</strong> Describe the Device Independent Bitmap
    format!</p>
  </section>



  <section><title>Macintosh Clipboard Data</title>
   <p><strong>FIXME:</strong> Describe the Macintosh clipboard formats!</p>
  </section>

 </body>
</document>

<!-- Keep this comment at the end of the file
Local variables:
mode: xml
sgml-omittag:nil
sgml-shorttag:nil
sgml-namecase-general:nil
sgml-general-insert-case:lower
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:1
sgml-indent-data:t
sgml-parent-document:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
