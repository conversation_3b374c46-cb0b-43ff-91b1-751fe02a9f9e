<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd">

<document>
 <header>
  <title>To Do</title>
  <authors>
   <person name="Rainer Klute" email="<EMAIL>"/>
  </authors>
 </header>
 <body>
  <section><title>To Do</title>

   <p>The following functionalities should be added to HPFS:</p>

   <ol>
    <li>
     Improve writing support! We need convenience classes and methods for
     easily writing summary information streams and document summary
     information streams.
    </li>
    <li>
     Add resource bundles to
     <code>org.apache.poi.hpsf.wellknown</code> to ease
     localizations. This would be useful for mapping standard property IDs to
     localized strings. Example: The property ID 4 could be mapped to "Author"
     in English or "Verfasser" in German.
    </li>
    <li>
     Implement reading functionality for those property types that are not
     yet supported. HPSF should return proper Java types instead of just byte
     arrays.
    </li>
    <li>
     Add WMF to <code>java.awt.Image</code> example code in the <a
      href="thumbnails.html">Thumbnail HOW-TO</a>.
    </li>
   </ol>
  </section>
 </body>
</document>

<!-- Keep this comment at the end of the file
Local variables:
mode: xml
sgml-omittag:nil
sgml-shorttag:nil
sgml-namecase-general:nil
sgml-general-insert-case:lower
sgml-minimize-attributes:nil
sgml-always-quote-attributes:t
sgml-indent-step:1
sgml-indent-data:t
sgml-parent-document:nil
sgml-exposed-tags:nil
sgml-local-catalogs:nil
sgml-local-ecat-files:nil
End:
-->
