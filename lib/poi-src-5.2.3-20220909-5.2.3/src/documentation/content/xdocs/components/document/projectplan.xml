<?xml version="1.0"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
   ====================================================================
-->
<!-- edited with XMLSPY v5 rel. 4 U (http://www.xmlspy.com) by <PERSON> (Myself) -->
<!DOCTYPE document PUBLIC "-//APACHE//DTD Documentation V2.0//EN" "document-v20.dtd">
<document>
 <header>
  <title>Apache POI - HWPF - Java API to Handle Microsoft Word Files</title>
  <subtitle>Project Plan</subtitle>
  <authors>
   <person name="Ryan Ackley" email="<EMAIL>"/>
  </authors>
 </header>
	<body>
		<p>HWPF Milestones</p>
		<table>
			<tr>
				<th>
					Milestones
				</th>
				<th>
					Target Date
				</th>
				<th>
					Owner
				</th>
			</tr>
			<tr>
				<td>
					Read in a Word document
with minimum formatting
(no lists, tables, footnotes,
endnotes, headers, footers)
and write it back out with the
result viewable in Word
97/2000
				</td>
				<td>
					07/11/2003
				</td>
				<td>
					Ryan
				</td>
			</tr>			
			<tr>
				<td>
					Add support for Lists and
Tables
				</td>
				<td>
					8/15/2003
				</td>
				<td>
					&#160;
				</td>
			</tr>
			<tr>
				<td>
					HWPF 1.0-alpha release with
documentation and examples
				</td>
				<td>
					8/18/2003
				</td>
				<td>
					Praveen/Ryan
				</td>
			</tr>
			<tr>
				<td>
					Add support for Headers,
Footers, endnotes, and
footnotes 
				</td>
				<td>
					8/31/2003
				</td>
				<td>
					?
				</td>
			</tr>
			<tr>
				<td>
					Add support for forms and
mail merge
				</td>
				<td>
					September/October 2003
				</td>
				<td>
					?
				</td>
			</tr>
		</table>
		<p>HWPF Task Lists</p>
		<p>Read in a Word document with minimum formatting (no lists, tables, footnotes,
endnotes, headers, footers) and write it back out with the result viewable in Word 97/2000</p>
		<table>
			<tr>
				<th>
					Task
				</th>
				<th>
					Target Date
				</th>
				<th>
					Owner
				</th>
			</tr>
			<tr>
				<td>
					Create classes to read and
write low level data
structures with test cases
				</td>
				<td>
					7/10/2003
				</td>
				<td>
					Ryan
				</td>
			</tr>
			<tr>
				<td>
					Create classes to read and
write FontTable and Font
names with test case
				</td>
				<td>
					7/10/2003
				</td>
				<td>
					Praveen
				</td>
			</tr>
			<tr>
				<td>
					Final test
				</td>
				<td>
					7/11/2003
				</td>
				<td>
					Ryan
				</td>
			</tr>
		</table>
		<p>Develop user friendly API so it is fun and easy to read and write word documents
with java.</p>
		<table>
			<tr>
				<th>
					Task
				</th>
				<th>
					Target Date
				</th>
				<th>
					Owner
				</th>
			</tr>
			<tr>
				<td>
					Develop a way for SPRMS to
be compressed and
uncompressed
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Override CHPAbstractType
with a concrete class that
exposes attributes with
human readable names
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Override PAPAbstractType
with a concrete class that
exposes attributes with
human readable names
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Override SEPAbstractType
with a concrete class that
exposes attributes with
human readable names
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Override DOPAbstractType
with a concrete class that
exposes attributes with
human readable names
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Override TAPAbstractType
with a concrete class that
exposes attributes with
human readable names
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Override TCAbstractType
with a concrete class that
exposes attributes with
human readable names
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Develop a VerifyIntegrity
class for testing so it is easy
to determine if a Word
Document is well-formed.
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Develop general intuitive
API to tie everything together
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
		</table>
		<p>Add support for lists and tables</p>
		<table>
			<tr>
				<th>
					Task
				</th>
				<th>
					Target Date
				</th>
				<th>
					Owner
				</th>
			</tr>
			<tr>
				<td>
					Add data structures for
reading and writing list data
with test cases.
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Add data structures for
reading and writing tables
with test cases.
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
		</table>
		<p>HWPF 1.0-alpha release with documentation and examples</p>
		<table>
			<tr>
				<th>
					Task
				</th>
				<th>
					Target Date
				</th>
				<th>
					Owner
				</th>
			</tr>
			<tr>
				<td>
					Document the user model
API
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Document the low level
classes
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
			<tr>
				<td>
					Come up with detailed How-To&#8217;s
				</td>
				<td>
					
				</td>
				<td>
					
				</td>
			</tr>
		</table>
	</body>
</document>
