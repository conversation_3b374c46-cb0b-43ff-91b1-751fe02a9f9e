<!--
  Licensed to the Apache Software Foundation (ASF) under one or more
  contributor license agreements.  See the NOTICE file distributed with
  this work for additional information regarding copyright ownership.
  The ASF licenses this file to You under the Apache License, Version 2.0
  (the "License"); you may not use this file except in compliance with
  the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<!-- ===================================================================

     Apache Changes POI DTD

PURPOSE:
  This DTD was developed to create a simple yet powerful document
  type for software development changes for use with the Apache projects.
  It is an XML-compliant DTD and it's maintained by the Apache POI
  project.

TYPICAL INVOCATION:

  <!DOCTYPE document PUBLIC
       "-//APACHE//DTD Changes POI//EN"
       "changes-poi.dtd">

NOTES:
  It is important, expecially in open developped software projects, to keep
  track of software changes both to give users indications of bugs that might
  have been resolved, as well, and not less important, to provide credits
  for the support given to the project. It is considered vital to provide
  adequate payback using recognition and credits to let users and developers
  feel part of the community, thus increasing development power.
==================================================================== -->

<!-- =============================================================== -->
<!-- Include the Documentation DTD -->
<!-- =============================================================== -->

<!ENTITY % document PUBLIC
    "-//APACHE//ENTITIES Documentation V2.0//EN"
    "document-v20.mod">
%document;

<!-- =============================================================== -->
<!-- Include the Common ISO Character Entity Sets -->
<!-- =============================================================== -->

<!ENTITY % common-charents PUBLIC
    "-//APACHE//ENTITIES Common Character Entity Sets V1.0//EN"
    "common-charents-v10.mod">
%common-charents;

<!-- =============================================================== -->
<!-- Include the Changes module -->
<!-- =============================================================== -->

<!ENTITY % changes PUBLIC
    "-//APACHE//ENTITIES Changes POI//EN"
    "changes-poi.mod">
%changes;

<!-- =============================================================== -->
<!-- End of DTD -->
<!-- =============================================================== -->
