# (C) Copyright U<PERSON> (Generics Policeman) and others.
# Parts of this work are licensed to the Apache Software Foundation (ASF)
# under one or more contributor license agreements.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# This file contains API signatures which are specific to POI.
# The goal is to minimize implicit defaults

@defaultMessage POI forbidden APIs which are not tolerated in production code

# We have applications which use this to return error codes on invalid commandline parameters...
#@defaultMessage Please do not terminate the application
#java.lang.System#exit(int)
#java.lang.Runtime#exit(int)
#java.lang.Runtime#halt(int)


java.lang.System#gc() @ Please do not try to stop the world
java.lang.Throwable#printStackTrace() @ Please use Log4J 2.x for exceptions
java.lang.Throwable#printStackTrace(java.io.PrintStream) @ Please use Log4J 2.x for exceptions
java.lang.Throwable#printStackTrace(java.io.PrintWriter) @ Please use Log4J 2.x for exceptions