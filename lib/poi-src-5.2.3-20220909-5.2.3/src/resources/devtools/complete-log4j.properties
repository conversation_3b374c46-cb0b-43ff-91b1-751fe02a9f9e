# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#log4j.rootCategory=debug,  globalLog
log4j.category.org.apache.poi.hssf=debug,  hssfLog
log4j.category.org.apache.poi.hdf=debug,  hdfLog
log4j.category.org.apache.poi.hpsf=debug,  hpsfLog
log4j.category.org.apache.poi.poifs=debug,  poifsLog
log4j.category.org.apache.poi.util=debug,  utilLog

log4j.appender.hssfLog=org.apache.log4j.FileAppender
log4j.appender.hssfLog.File=build/hssf.log
log4j.appender.hssfLog.layout=org.apache.log4j.PatternLayout
log4j.appender.hssfLog.layout.ConversionPattern=%-4r [%t] %-5p %c %x - %m%n

log4j.appender.hdfLog=org.apache.log4j.FileAppender
log4j.appender.hdfLog.File=build/hdf.log
log4j.appender.hdfLog.layout=org.apache.log4j.PatternLayout
log4j.appender.hdfLog.layout.ConversionPattern=%-4r [%t] %-5p %c %x - %m%n

log4j.appender.hpsfLog=org.apache.log4j.FileAppender
log4j.appender.hpsfLog.File=build/hpsf.log
log4j.appender.hpsfLog.layout=org.apache.log4j.PatternLayout
log4j.appender.hpsfLog.layout.ConversionPattern=%-4r [%t] %-5p %c %x - %m%n

log4j.appender.poifsLog=org.apache.log4j.FileAppender
log4j.appender.poifsLog.File=build/poifs.log
log4j.appender.poifsLog.layout=org.apache.log4j.PatternLayout
log4j.appender.poifsLog.layout.ConversionPattern=%-4r [%t] %-5p %c %x - %m%n

log4j.appender.utilLog=org.apache.log4j.FileAppender
log4j.appender.utilLog.File=build/util.log
log4j.appender.utilLog.layout=org.apache.log4j.PatternLayout
log4j.appender.utilLog.layout.ConversionPattern=%-4r [%t] %-5p %c %x - %m%n
