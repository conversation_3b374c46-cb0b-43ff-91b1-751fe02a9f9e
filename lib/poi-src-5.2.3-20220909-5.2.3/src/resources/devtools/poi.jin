###
### Jindent 3.2x property file -- http://www.jindent.de
###
### this encapsulates my preferred style, plus project-specific style ...
###
### author: <PERSON>
###

### General -- Convention

conventionName                            = ""
conventionString                          = ""
conventionNotePosition                    = "none"
blankLinesToSeparateConventionNote        = 2

### General -- Jindent Note

jindentNotePosition                       = "none"
blankLinesToSeparateJindentNote           = 2

### Header/Footer -- Header Template

headerSmartMode                           = infinite
headerIdentifyKey                         = "Copyright (c) 2002-2006 The Apache Software Foundation"
blankLinesBeforeHeader                    = 1
header[00]="/* ==================================================================="
header[01]=" * Licensed to the Apache Software Foundation (ASF) under one"
header[02]=" * or more contributor license agreements.  See the NOTICE file"
header[03]=" * distributed with this work for additional information"
header[04]=" * regarding copyright ownership.  The ASF licenses this file"
header[05]=" * to you under the Apache License, Version 2.0 (the"
header[06]=" * \"License\"); you may not use this file except in compliance"
header[07]=" * with the License.  You may obtain a copy of the License at"
header[08]=" * "
header[09]=" * http://www.apache.org/licenses/LICENSE-2.0"
header[10]=" * "
header[11]=" * Unless required by applicable law or agreed to in writing,"
header[12]=" * software distributed under the License is distributed on an"
header[13]=" * \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY"
header[14]=" * KIND, either express or implied.  See the License for the"
header[15]=" * specific language governing permissions and limitations"
header[16]=" * under the License"
header[17]=" */"
blankLinesAfterHeader                     = 1

### Header/Footer -- Footer Template

footerSmartMode                           = infinite
footerIdentifyKey                         = ""
blankLinesBeforeFooter                    = 0
footer[00]                                = ""
blankLinesAfterFooter                     = 0

### Indentation -- Misc

tabulatorSize                             = 8
indentSize                                = 4
firstLevelIndent                          = 0
indentCaseFromSwitch                      = true
labelNewLine                              = true
indentLabels                              = true
minimumCommentIndent                      = 3
indentLeadingsByTabs                      = false
indentCommentsByTabs                      = false
indentDeclarationsByTabs                  = false
indentAssignmentsByTabs                   = false

### Indentation -- Alignment

alignComments                             = true
alignDeclarations                         = true
alignAssignments                          = true
alignTernaryConditions                    = false
alignTernaryExpressions                   = true
alignTooLongComments                      = true

### Braces -- Style

leftBraceNewLineGeneral                   = true
rightBraceNewLineGeneral                  = true
indentLeftBraceGeneral                    = 0
indentRightBraceGeneral                   = 0
indentAfterRightBraceGeneral              = 0
cuddleEmptyBracesGeneral                  = false
indentCuddledBracesGeneral                = 0

leftBraceNewLineClassInterface            = true
rightBraceNewLineClassInterface           = true
indentLeftBraceClassInterface             = 0
indentRightBraceClassInterface            = 0
indentAfterRightBraceClassInterface       = 0
cuddleEmptyBracesClassInterface           = false
indentCuddledBracesClassInterface         = 0

leftBraceNewLineMethod                    = true
rightBraceNewLineMethod                   = true
indentLeftBraceMethod                     = 0
indentRightBraceMethod                    = 0
indentAfterRightBraceMethod               = 0
cuddleEmptyBracesMethod                   = false
indentCuddledBracesMethod                 = 0

leftBraceNewLineTryCatch                  = true
rightBraceNewLineTryCatch                 = true
indentLeftBraceTryCatch                   = 0
indentRightBraceTryCatch                  = 0
indentAfterRightBraceTryCatch             = 0
cuddleEmptyBracesTryCatch                 = false
indentCuddledBracesTryCatch               = 0

### Braces -- Insert At

insertBracesAtIfElse                      = true
insertBracesAtFor                         = true
insertBracesAtWhile                       = true
insertBracesAtDoWhile                     = true
insertParenthesisAtConditions             = true

### Braces -- If-Else

singleIfStatementInOneLine                = false
singleElseStatementInOneLine              = false
specialElseIfTreatment                    = true

### JavaDoc -- Misc

deleteJavaDocComments                     = false
formatJavaDocComments                     = true
insertMissingJavaDocTags                  = true
deleteObsoleteJavaDocTags                 = false
createPublicClassInterfaceJavaDocs        = true
createFriendlyClassInterfaceJavaDocs      = false
createPrivateClassInterfaceJavaDocs       = false
createProtectedClassInterfaceJavaDocs     = false
createPublicMethodJavaDocs                = false
createFriendlyMethodJavaDocs              = false
createPrivateMethodJavaDocs               = false
createProtectedMethodJavaDocs             = false
createPublicFieldJavaDocs                 = false
createFriendlyFieldJavaDocs               = false
createPrivateFieldJavaDocs                = false
createProtectedFieldJavaDocs              = false

### JavaDoc -- Templates

sortExceptionsInTemplates                 = true
javaDocMethodTop[00]                      = "/**"
javaDocMethodTop[01]                      = " * Method $objectName$"
javaDocMethodTop[02]                      = " *"
javaDocMethodParamSeparator[00]           = " *"
javaDocMethodParam[00]                    = " * @param $paramName$"
javaDocMethodReturn[00]                   = " *"
javaDocMethodReturn[01]                   = " * @return"
javaDocMethodExceptionSeparator[00]       = " *"
javaDocMethodException[00]                = " * @throws $exceptionName$"
javaDocMethodBottom[00]                   = " *"
javaDocMethodBottom[01]                   = " */"
javaDocConstructorTop[00]                 = "/**"
javaDocConstructorTop[01]                 = " * Constructor $objectName$"
javaDocConstructorTop[02]                 = " *"
javaDocConstructorParamSeparator[00]      = " *"
javaDocConstructorParam[00]               = " * @param $paramName$"
javaDocConstructorExceptionSeparator[00]  = " *"
javaDocConstructorException[00]           = " * @throws $exceptionName$"
javaDocConstructorBottom[00]              = " *"
javaDocConstructorBottom[01]              = " */"
javaDocClass[00]                          = "/**"
javaDocClass[01]                          = " * Class $objectName$"
javaDocClass[02]                          = " *"
javaDocClass[03]                          = " *"
javaDocClass[04]                          = " * @author"
javaDocClass[05]                          = " * @version %I%, %G%"
javaDocClass[06]                          = " */"
javaDocInterface[00]                      = "/**"
javaDocInterface[01]                      = " * Interface $objectName$"
javaDocInterface[02]                      = " *"
javaDocInterface[03]                      = " *"
javaDocInterface[04]                      = " * @author"
javaDocInterface[05]                      = " * @version %I%, %G%"
javaDocInterface[06]                      = " */"
javaDocField[00]                          = "/** Field $objectName$           */"

### Comments -- Format/Delete

deleteBlockComments                       = false
deleteSingleLineComments                  = false
deleteTrailingComments                    = false
deleteEndOfLineComments                   = false
formatBlockComments                       = true
formatSingleLineComments                  = true
formatTrailingComments                    = true
formatEndOfLineComments                   = true

### Comments -- Exceptions

neverIndentFirstColumnComments            = true
neverFormatFirstColumnComments            = true
neverFormatHeader                         = false
neverFormatFooter                         = false

### Separation -- Misc

keepBlankLines                            = 0
minLinesToInsertBlankLineInClasses        = infinite
minLinesToInsertBlankLineInMethods        = infinite

### Separation -- Separate

separateChunksByComments                  = false
allowBreakSeparatedFromCaseBlock          = false
blankLinesBetweenCaseBlocks               = 1
blankLinesBetweenChunks                   = 0
comparableImportDepth                     = 2
blankLinesToSeparateImports               = 1
blankLinesBetweenClassInterface           = 2

### Separation -- Insert Blank Lines

blankLinesAfterDeclarations               = 1
blankLinesAfterMethods                    = 1
blankLinesAfterClasses                    = 1
blankLinesAfterInterfaces                 = 1
blankLinesBeforeJavaDocComments           = 1
blankLinesAfterJavaDocComments            = 1
blankLinesBeforeBlockComments             = 1
blankLinesAfterBlockComments              = 0
blankLinesBeforeSingleLineComments        = 1
blankLinesAfterSingleLineComments         = 0
blankLinesBeforeEndOfLineComments         = 1
blankLinesAfterEndOfLineComments          = 0
blankLinesAfterSwitch                     = 1
blankLinesAfterPackage                    = 1
blankLinesAfterLastImport                 = 1

### Whitespaces -- Padding

separateAssignmentOperators               = true
separateConditionalOperators              = true
separateComparisonOperators               = true
separateNumericalOperators                = true
paddingCastParenthesis                    = true
paddingParenthesis                        = false
paddingBrackets                           = true
paddingBraces                             = true

### Whitespaces -- Space Before

spaceBeforeMethodDeclarationParenthesis   = false
spaceBeforeMethodCallParenthesis          = false
spaceBeforeBrackets                       = false
spaceBeforeBracketsInTypes                = true
spaceBeforeStatementParenthesis           = true
spaceBeforeConditionBang                  = true
spaceBeforeCaseColon                      = true

### Whitespaces -- Space After

spaceAfterComma                           = true
spaceAfterSemicolon                       = true
spaceAfterCasting                         = true

### Whitespaces -- No Spaces

noSpacesInEmptyForExpressions             = true

### Line Wrapping -- Misc

maxFieldElementsPerLine                   = 0
wrapLines                                 = true
wrapBecauseOfComments                     = true
wrapLongMethodNames                       = true
maxLineLength                             = 78
deepIndent                                = 45
forceIndent                               = 8
forceIndentTolerance                      = 4
allowWrappingAfterAssignments             = true
allowWrappingAfterParenthesis             = true
preferWrappingAfterThrows                 = true
alwaysWrapThrows                          = true
alwaysWrapExtends                         = true
alwaysWrapImplements                      = true
indentWrappedThrows                       = 4
indentWrappedExtends                      = 4
indentWrappedImplements                   = 4
