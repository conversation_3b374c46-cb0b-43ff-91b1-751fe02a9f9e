# This workflow will build a Java project with <PERSON>radle
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-gradle

name: Java CI with Gradle

on:
  push:
    branches: [ trunk ]
  pull_request:
    branches: [ trunk ]

permissions: {}

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK
      uses: actions/setup-java@v3
      with:
        distribution: 'temurin'
        java-version: '11'
        cache: 'gradle'

    - name: Verify Gradle wrapper
      uses: gradle/wrapper-validation-action@v1

    - name: Grant execute permission for gradlew
      run: chmod +x gradlew

    - name: Build with Gradle
      run: ./gradlew check -PjdkVersion=11 --no-daemon --refresh-dependencies
