######################################################
# Rich text with extended information test

[rich-header]
1D 00                                               # String length 0x1b=29
0D                                                  # Option flag, rich text + 16bit + extended
02 00                                               # Formatting runs
03 00 00 00                                         # Far east data size
# String: At a dinner party or
41 00 74 00 20 00 61 00 20 00
64 00 69 00 6E 00 6E 00 65 00
72 00 20 00 70 00 61 00 72 00
74 00 79 00 20 00 6F 00 72 00

[rich-continue1]
# Continuation record
00                                                  # option flag

# string:at at at
41 74 20
41 74 20
41 74 20

00 00                                               # Formatting run 1, first formatted char at 0
00 00                                               # Formatting run 1, Index to font record
02 00                                               # Formatting run 2, first formatted char at 2
00 00                                               # Formatting run 2, Index to font record

FF FF FF                                            # extended data


########################################################
# Normal text with extended information.

[norich-header]
1D 00                                               # String length 0x1b=29
05                                                  # Option flag, 16bit + extended
03 00 00 00                                         # Far east data size
# String: At a dinner party or
41 00 74 00 20 00 61 00 20 00
64 00 69 00 6E 00 6E 00 65 00
72 00 20 00 70 00 61 00 72 00
74 00 79 00 20 00 6F 00 72 00

[norich-continue1]
# Continuation record
00                                                  # option flag

# string:at at at
41 74 20
41 74 20
41 74 20

FF FF FF                                            # extended data

