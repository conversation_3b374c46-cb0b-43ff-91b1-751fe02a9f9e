/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

import java.util.regex.Pattern

sourceSets {
    main {
        if (jdkVersion > 8) {
            output.dir(JAVA9_OUT, builtBy: 'cacheJava9')
        }
    }
}

dependencies {
    api project(':poi-ooxml')
    implementation project(path: ':poi', configuration: 'archives')
    compileOnly project(path: ':poi-ooxml', configuration: 'archives')
    compileOnly project(path: ':poi-ooxml-full', configuration: 'archives')

    if (NO_SCRATCHPAD) {
        compileOnly project(path: ':poi-scratchpad', configuration: 'archives')
    } else {
        implementation project(path: ':poi-scratchpad', configuration: 'archives')
    }

    implementation "org.apache.logging.log4j:log4j-api:${log4jVersion}"

    testImplementation(project(path: ':poi-ooxml', configuration: 'tests')) {
        if (NO_SCRATCHPAD) {
            exclude group: 'org.apache.poi', module: 'poi-scratchpad'
        }
    }
    testImplementation project(path: ':poi', configuration: 'tests')

    if (SAXON_TEST) {
         testRuntimeOnly "net.sf.saxon:Saxon-HE:${saxonVersion}"
    }
}

final String MODULE_NAME = 'org.apache.poi.examples'
final Pattern MODULE_REGEX = ~'\\.jar$'
final List MODULE_COMPILE_PATH = sourceSets.main.compileClasspath.findAll{ it.path =~ MODULE_REGEX }.collect{ it.parent }.unique()

task compileJava9(type: JavaCompile) {
    dependsOn 'compileJava', ':poi-ooxml:jar', ':poi-scratchpad:jar'

    javaCompiler = javaToolchains.compilerFor {
        languageVersion = JavaLanguageVersion.of(jdkVersion >= 17 ? 17 : 11)
        if (jdkVendor != '') vendor = JvmVendorSpec.matching(jdkVendor)
    }

    destinationDirectory = file(JAVA9_OUT + VERSIONS9)
    source = file(JAVA9_SRC)
    classpath = files()
    options.compilerArgs = [
        '--patch-module', "${MODULE_NAME}=${sourceSets.main.output.classesDirs.asPath}",
        '--module-path', files(MODULE_COMPILE_PATH).asPath
    ]


    onlyIf {
        jdkVersion > 8
    }
}

task cacheJava9(type: Copy) {
    dependsOn 'compileJava9'

    from(file(JAVA9_OUT + VERSIONS9))
    into(JAVA9_SRC)
}

jar {
    dependsOn cacheJava9

    destinationDirectory = file("../build/dist/maven/${project.archivesBaseName}")

    if (jdkVersion == 8) {
        into('META-INF/versions/9') {
            from JAVA9_SRC include '*.class'
        }
    }

    manifest {
        attributes('Automatic-Module-Name': MODULE_NAME, 'Multi-Release': 'true')
    }
}

javadocJar {
    metaInf {
        from("$projectDir/../legal/LICENSE")
        from("$projectDir/../legal/NOTICE")
    }
}

sourcesJar {
    metaInf {
        from("$projectDir/../legal/LICENSE")
        from("$projectDir/../legal/NOTICE")
    }
}
