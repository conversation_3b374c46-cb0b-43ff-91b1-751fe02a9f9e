/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

package org.apache.poi.examples.xssf.usermodel;

import java.io.FileOutputStream;
import java.io.IOException;

import org.apache.poi.ss.usermodel.PaneType;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * How to set split and freeze panes
 */
public class SplitAndFreezePanes {
    public static void main(String[]args) throws IOException {
        try (Workbook wb = new XSSFWorkbook()) {
            Sheet sheet1 = wb.createSheet("new sheet");
            Sheet sheet2 = wb.createSheet("second sheet");
            Sheet sheet3 = wb.createSheet("third sheet");
            Sheet sheet4 = wb.createSheet("fourth sheet");

            // Freeze just one row
            sheet1.createFreezePane(0, 1, 0, 1);
            // Freeze just one column
            sheet2.createFreezePane(1, 0, 1, 0);
            // Freeze the columns and rows (forget about scrolling position of the lower right quadrant).
            sheet3.createFreezePane(2, 2);
            // Create a split with the lower left side being the active quadrant
            sheet4.createSplitPane(2000, 2000, 0, 0, PaneType.LOWER_LEFT);

            try (FileOutputStream fileOut = new FileOutputStream("splitFreezePane.xlsx")) {
                wb.write(fileOut);
            }
        }
    }
}
