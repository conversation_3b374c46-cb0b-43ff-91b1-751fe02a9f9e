/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */
package org.apache.poi.examples.xssf.usermodel;

import java.io.FileOutputStream;
import java.io.IOException;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * How to use newlines in cells
 */
public class NewLinesInCells {

    public static void main(String[]args) throws IOException {
        try (Workbook wb = new XSSFWorkbook()) {   //or new HSSFWorkbook();
            Sheet sheet = wb.createSheet();

            Row row = sheet.createRow(2);
            Cell cell = row.createCell(2);
            cell.setCellValue("Use \n with word wrap on to create a new line");

            //to enable newlines you need set a cell styles with wrap=true
            CellStyle cs = wb.createCellStyle();
            cs.setWrapText(true);
            cell.setCellStyle(cs);

            //increase row height to accommodate two lines of text
            row.setHeightInPoints(2 * sheet.getDefaultRowHeightInPoints());

            //adjust column width to fit the content
            sheet.autoSizeColumn(2);

            try (FileOutputStream fileOut = new FileOutputStream("ooxml-newlines.xlsx")) {
                wb.write(fileOut);
            }
        }
    }
}
