/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

package org.apache.poi.xslf.usermodel;

import org.apache.poi.ooxml.util.POIXMLUnits;
import org.apache.poi.sl.usermodel.TabStop;
import org.apache.poi.util.Units;
import org.openxmlformats.schemas.drawingml.x2006.main.CTTextTabStop;
import org.openxmlformats.schemas.drawingml.x2006.main.STTextTabAlignType;

public class XSLFTabStop implements TabStop {

    final CTTextTabStop tabStop;

    XSLFTabStop(CTTextTabStop tabStop) {
        this.tabStop = tabStop;
    }

    /** position in EMUs */
    public int getPosition() {
        return (int)POIXMLUnits.parseLength(tabStop.xgetPos());
    }

    /** position in EMUs */
    public void setPosition(final int position) {
        tabStop.setPos(position);
    }

    @Override
    public double getPositionInPoints() {
        return Units.toPoints(getPosition());
    }

    @Override
    public void setPositionInPoints(final double points) {
        setPosition(Units.toEMU(points));
    }

    public TabStopType getType() {
        return TabStopType.fromOoxmlId(tabStop.getAlgn().intValue());
    }

    public void setType(final TabStopType tabStopType) {
        tabStop.setAlgn(STTextTabAlignType.Enum.forInt(tabStopType.ooxmlId) );
    }
}
