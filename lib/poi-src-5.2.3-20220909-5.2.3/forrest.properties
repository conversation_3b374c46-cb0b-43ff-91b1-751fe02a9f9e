# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

##############
# These are the defaults, un-comment them only if you need to change them.
#
# You can even have a completely empty file, to assist with maintenance.
# This file is required, even if empty.
#
# The file obtained from 'forrest seed-sample' shows the defaults.
##############

# Prints out a summary of Forrest settings for this project
forrest.echo=true 

# Project name (used to name .war file)
#project.name=my-project

# Specifies name of <PERSON> skin to use
# See list at http://forrest.apache.org/docs/skins.html
#project.skin=pelt

# codename: Dispatcher
# Dispatcher is using a fallback mechanism for theming.
# You can configure the theme name and its extension here
#project.theme-extension=.fv
#project.theme=pelt


# Descriptors for plugins and skins
# comma separated list, file:// is supported
#forrest.skins.descriptors=http://forrest.apache.org/skins/skins.xml,file:///c:/myskins/skins.xml
#forrest.plugins.descriptors=http://forrest.apache.org/plugins/plugins.xml,http://forrest.apache.org/plugins/whiteboard-plugins.xml

##############
# behavioural properties
#project.menu-scheme=tab_attributes
#project.menu-scheme=directories

##############
# layout properties

# Properties that can be set to override the default locations
#
# Parent properties must be set. This usually means uncommenting
# project.content-dir if any other property using it is uncommented

#project.status=status.xml
#project.content-dir=${project.home}/src/documentation
#project.raw-content-dir=${project.content-dir}/content
#project.conf-dir=${project.content-dir}/conf
project.configfile=${project.home}/src/documentation/cli.xconf
#project.sitemap-dir=${project.content-dir}
#project.xdocs-dir=${project.content-dir}/content/xdocs
#project.resources-dir=${project.content-dir}/resources
#project.stylesheets-dir=${project.resources-dir}/stylesheets
#project.images-dir=${project.resources-dir}/images
#project.schema-dir=${project.resources-dir}/schema
#project.skins-dir=${project.content-dir}/skins
#project.skinconf=${project.content-dir}/skinconf.xml
#project.lib-dir=${project.content-dir}/lib
#project.classes-dir=${project.content-dir}/classes
#project.translations-dir=${project.content-dir}/translations

#project.build-dir=${project.home}/build
#project.site=site
#project.site-dir=${project.build-dir}/${project.site}
#project.temp-dir=${project.build-dir}/tmp

##############
# Cocoon catalog entity resolver properties
# A local OASIS catalog file to supplement the default Forrest catalog
#project.catalog=${project.schema-dir}/catalog.xcat

# The verbosity level for the entity resolver (1..10)
#forrest.catalog.verbosity=1


##############
# validation properties

# This set of properties determine if validation is performed
# Values are inherited unless overridden.
# e.g. if forrest.validate=false then all others are false unless set to true.
#forrest.validate=true
#forrest.validate.xdocs=${forrest.validate}
#forrest.validate.skinconf=${forrest.validate}
#forrest.validate.sitemap=${forrest.validate}
#forrest.validate.stylesheets=${forrest.validate}
#forrest.validate.skins=${forrest.validate}
#forrest.validate.skins.stylesheets=${forrest.validate.skins}

# *.failonerror=(true|false) - stop when an XML file is invalid
#forrest.validate.failonerror=true

# *.excludes=(pattern) - comma-separated list of path patterns to not validate
# Note: If you do add an "excludes" list then you need to specify site.xml too.
# e.g.
#forrest.validate.xdocs.excludes=site.xml, samples/subdir/**, samples/faq.xml
#forrest.validate.xdocs.excludes=site.xml


##############
# General Forrest properties

# The URL to start crawling from
#project.start-uri=linkmap.html

# Set logging level for messages printed to the console
# (DEBUG, INFO, WARN, ERROR, FATAL_ERROR)
#project.debuglevel=ERROR

# Max memory to allocate to Java
#forrest.maxmemory=64m

# Any other arguments to pass to the JVM. For example, to run on an X-less
# server, set to -Djava.awt.headless=true
#forrest.jvmargs=

# The bugtracking URL - the issue number will be appended
# Projects would use their own issue tracker, of course.
project.bugtracking-url=https://bz.apache.org/bugzilla/
project.github-url=https://github.com/apache/poi

# The issues list as rss
#project.issues-rss-url=

#I18n Property. Based on the locale request for the browser.
#If you want to use it for static site then modify the JVM system.language
# and run once per language
#project.i18n=false

# The names of plugins that are required to build the project
# comma separated list (no spaces)
# You can request a specific version by appending "-VERSION" to the end of
# the plugin name. If you exclude a version number, the latest released version
# will be used. However, be aware that this may be a development version. In
# a production environment it is recommended that you specify a known working
# version.
# Run "forrest available-plugins" for a list of plug-ins currently available.
project.required.plugins=org.apache.forrest.plugin.output.pdf

# codename: Dispatcher
# Add the following plugins to project.required.plugins:
#org.apache.forrest.plugin.internal.dispatcher

# Proxy configuration
# - proxy.user and proxy.password are only needed if the proxy is an authenticated one...
# proxy.host=myproxy.example.org
# proxy.port=<ProxyPort, if not the default : 80>
# proxy.user=<login, if authenticated proxy>
# proxy.password=<password, if authenticated proxy>
