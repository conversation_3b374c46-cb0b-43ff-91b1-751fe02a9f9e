/* ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
==================================================================== */

import java.util.regex.Pattern

configurations {
    tests
}

sourceSets {
    main {
        if (jdkVersion > 8) {
            output.dir(JAVA9_OUT, builtBy: 'cacheJava9')
        }
    }
    test {
        if (jdkVersion > 8) {
            output.dir(TEST9_OUT, builtBy: 'cacheTest9')
        }
    }
}

dependencies {
    api 'org.apache.ant:ant:1.10.12'

    api project(':poi-ooxml')
    compileOnly project(path: ':poi-ooxml', configuration: 'archives')

    testImplementation project(path: ':poi-ooxml-lite-agent', configuration: 'archives')
    testImplementation project(path: ':poi', configuration: 'tests')
    testImplementation(project(path: ':poi-ooxml', configuration: 'tests')) {
        exclude group: 'org.apache.poi', module: 'poi-scratchpad'
    }
    testImplementation 'com.google.guava:guava:31.1-jre'
    testImplementation "org.apache.logging.log4j:log4j-slf4j18-impl:${log4jVersion}"
    testImplementation 'org.slf4j:slf4j-simple:1.7.36'
    testRuntimeOnly "org.apiguardian:apiguardian-api:${apiGuardianVersion}"

    if (SAXON_TEST) {
        testRuntimeOnly "net.sf.saxon:Saxon-HE:${saxonVersion}"
    }
}

final String MODULE_NAME = 'org.apache.poi.excelant'
final Pattern MODULE_NOT_REGEX = ~'((poi|poi-ooxml|poi-scratchpad)[/\\\\][^/\\\\]+$|batik-script)'
final Pattern MODULE_REGEX = ~'\\.jar$'
final List MAIN_MODULE_PATH = sourceSets.main.runtimeClasspath.findAll{ it.path =~ MODULE_REGEX }.collect{ it.parent }.unique()
final List TEST_MODULE_PATH = configurations.testRuntimeClasspath.findAll{ it.path =~ MODULE_REGEX && !(it.path =~ MODULE_NOT_REGEX) }.collect{ it.parent }.unique()

final String OOXML_LITE_AGENT = "../build/dist/maven/poi-ooxml-lite-agent/poi-ooxml-lite-agent-${project.version}.jar"
final String OOXML_LITE_REPORT = '../src/resources/ooxml-lite-report'
final String OOXML_LITE_INCLUDES = "^(com/microsoft/schemas|org/(etsi|openxmlformats|w3/)|org/apache/poi/schemas)"

task compileJava9(type: JavaCompile) {
    dependsOn 'compileJava', ':poi-ooxml:jar', ':poi-scratchpad:jar'

    javaCompiler = javaToolchains.compilerFor {
        languageVersion = JavaLanguageVersion.of(jdkVersion >= 17 ? 17 : 11)
        if (jdkVendor != '') vendor = JvmVendorSpec.matching(jdkVendor)
    }
    destinationDirectory = file(JAVA9_OUT + VERSIONS9)
    source = file(JAVA9_SRC)
    classpath = files()
    options.compilerArgs = [
        '--patch-module', "${MODULE_NAME}=${sourceSets.main.output.classesDirs.asPath}",
        '--module-path', files(MAIN_MODULE_PATH).asPath
    ]

    onlyIf {
        jdkVersion > 8
    }
}

task cacheJava9(type: Copy) {
    dependsOn 'compileJava9'

    from(file(JAVA9_OUT + VERSIONS9))
    into(JAVA9_SRC)
}

task compileTest9(type: JavaCompile) {
    dependsOn 'compileTestJava', ':poi-ooxml:jar', ':poi-scratchpad:jar'

    javaCompiler = javaToolchains.compilerFor {
        languageVersion = JavaLanguageVersion.of(jdkVersion)
        if (jdkVendor != '') vendor = JvmVendorSpec.matching(jdkVendor)
    }
    destinationDirectory = file(TEST9_OUT + VERSIONS9)
    source = file(TEST9_SRC)
    options.compilerArgs = [
        '--patch-module', "${MODULE_NAME}=${(sourceSets.main.output.classesDirs + sourceSets.test.output.classesDirs).asPath}",
        '--module-path', files(TEST_MODULE_PATH).asPath
    ]
    classpath = files()

    onlyIf {
        jdkVersion > 8
    }
}


task cacheTest9(type: Copy) {
    dependsOn 'compileTest9'

    from(file(TEST9_OUT + VERSIONS9))
    into(TEST9_SRC)
}

jar {
    dependsOn cacheJava9

    destinationDirectory = file("../build/dist/maven/${project.archivesBaseName}")

    if (jdkVersion == 8) {
        into('META-INF/versions/9') {
            from JAVA9_SRC include '*.class'
        }
    }

    manifest {
        attributes('Automatic-Module-Name': MODULE_NAME, 'Multi-Release': 'true')
    }
}

javadocJar {
    metaInf {
        from("$projectDir/../legal/LICENSE")
        from("$projectDir/../legal/NOTICE")
    }
}

sourcesJar {
    metaInf {
        from("$projectDir/../legal/LICENSE")
        from("$projectDir/../legal/NOTICE")
    }
}

// Create a separate jar for test-code to depend on it in other projects
// See http://stackoverflow.com/questions/5144325/gradle-test-dependency
task testJar(type: Jar, dependsOn: [ testClasses, cacheTest9 ] ) {
    destinationDirectory = file("../build/dist/maven/${project.archivesBaseName}-tests")

    classifier 'tests'
    // ignore second module-info.class from main
    duplicatesStrategy = 'exclude'

    if (jdkVersion == 8) {
        into('META-INF/versions/9') {
            from TEST9_SRC include '*.class'
        }
    }

    from sourceSets.test.output + sourceSets.main.output

    manifest {
        attributes('Automatic-Module-Name': MODULE_NAME, 'Multi-Release': 'true')
    }
}

artifacts {
    tests testJar
}

test {
    dependsOn { testJar }

    doFirst {
        jvmArgs += [
            "-javaagent:${OOXML_LITE_AGENT}=${OOXML_LITE_REPORT}|${OOXML_LITE_INCLUDES}",
        ]
        if (jdkVersion > 8) {
            jvmArgs += [
                '--add-modules', MODULE_NAME,
                '--module-path', '../build/dist/maven/poi-excelant-tests' + File.pathSeparator + files(TEST_MODULE_PATH).asPath,
            ]
        }
    }
}

publishing {
    publications {
        POI(MavenPublication) {
            pom {
                name = 'Apache POI - ExcelAnt'
                description = 'Apache POI - Java API To Access Microsoft Format Files'
            }
        }
    }
}
