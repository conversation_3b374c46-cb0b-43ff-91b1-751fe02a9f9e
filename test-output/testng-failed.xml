<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd">
<suite name="Failed suite [Intro]" guice-stage="DEVELOPMENT">
  <listeners>
    <listener class-name="Listeners.WebDriverListeners"/>
  </listeners>
  <test thread-count="5" name="CQ(failed)">
    <classes>
      <class name="CQ.Login_page_Alerts">
        <methods>
          <include name="f2"/>
        </methods>
      </class> <!-- CQ.Login_page_Alerts -->
    </classes>
  </test> <!-- CQ(failed) -->
</suite> <!-- Failed suite [Intro] -->
